{% extends "layout.html" %}

{% block title %}Edit Fee Record - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Edit Fee <PERSON>er -->
        <div class="page-header warning">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title">
                            <i class="bi bi-pencil"></i>
                            Edit Fee Record
                        </h1>
                        <p class="page-subtitle">Update fee record information and payment details for the selected student.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions">
                            <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Fees
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Fee Information -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            Current Fee Information
                        </h2>
                    </div>

                    <div class="info-display">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Student Name</label>
                                    <div class="info-value">{{ fee.student_name or 'John Smith' }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Student ID</label>
                                    <div class="info-value">
                                        <span class="status-badge student">{{ fee.student_id or 'STU001' }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Current Status</label>
                                    <div class="info-value">
                                        <span class="status-badge {{ 'paid' if (fee.status or 'pending') == 'paid' else 'unpaid' if (fee.status or 'pending') == 'overdue' else 'pending' }}">
                                            <i class="bi bi-{{ 'check-circle' if (fee.status or 'pending') == 'paid' else 'x-circle' if (fee.status or 'pending') == 'overdue' else 'clock' }}"></i>
                                            {{ (fee.status or 'pending').title() }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Created Date</label>
                                    <div class="info-value text-muted">
                                        {{ fee.created_at|datetime if fee.created_at else 'Dec 15, 2023' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit Form -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-currency-dollar"></i>
                            Update Fee Information
                        </h2>
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row g-4 mb-5">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="fee_type" name="fee_type" required>
                                        <option value="">Select Fee Type</option>
                                        <option value="Tuition" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Tuition' }}>Tuition Fee</option>
                                        <option value="Lab" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Lab' }}>Lab Fee</option>
                                        <option value="Library" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Library' }}>Library Fee</option>
                                        <option value="Sports" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Sports' }}>Sports Fee</option>
                                        <option value="Exam" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Exam' }}>Exam Fee</option>
                                        <option value="Transport" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Transport' }}>Transport Fee</option>
                                        <option value="Hostel" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Hostel' }}>Hostel Fee</option>
                                        <option value="Other" {{ 'selected' if (fee.fee_type or 'Tuition') == 'Other' }}>Other</option>
                                    </select>
                                    <label for="fee_type">
                                        <i class="bi bi-tag me-2"></i>Fee Type *
                                    </label>
                                    <div class="invalid-feedback">
                                        Please select fee type.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="amount" name="amount"
                                           value="{{ fee.amount or 2500.00 }}" placeholder="Amount" required min="0.01" step="0.01">
                                    <label for="amount">
                                        <i class="bi bi-currency-dollar me-2"></i>Amount *
                                    </label>
                                    <div class="invalid-feedback">
                                        Please enter a valid amount.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-4 mb-5">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="due_date" name="due_date"
                                           value="{{ fee.due_date or '2024-01-31' }}" placeholder="Due Date" required>
                                    <label for="due_date">
                                        <i class="bi bi-calendar me-2"></i>Due Date *
                                    </label>
                                    <div class="invalid-feedback">
                                        Please select due date.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="status" name="status">
                                        <option value="pending" {{ 'selected' if (fee.status or 'pending') == 'pending' }}>Pending</option>
                                        <option value="paid" {{ 'selected' if (fee.status or 'pending') == 'paid' }}>Paid</option>
                                        <option value="overdue" {{ 'selected' if (fee.status or 'pending') == 'overdue' }}>Overdue</option>
                                    </select>
                                    <label for="status">
                                        <i class="bi bi-check-circle me-2"></i>Payment Status
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-5">
                            <textarea class="form-control" id="remarks" name="remarks"
                                      placeholder="Remarks" style="height: 100px;">{{ fee.remarks or '' }}</textarea>
                            <label for="remarks">
                                <i class="bi bi-chat-text me-2"></i>Remarks (Optional)
                            </label>
                        </div>

                        <!-- Payment Information (if paid) -->
                        {% if (fee.status or 'pending') == 'paid' %}
                        <div class="alert alert-success mb-5">
                            <h6 class="alert-heading">
                                <i class="bi bi-check-circle me-2"></i>Payment Information
                            </h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <strong>Payment Date:</strong> {{ fee.paid_date|date if fee.paid_date else 'Dec 20, 2023' }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Payment Method:</strong> {{ fee.payment_method or 'Cash' }}
                                </div>
                            </div>
                            <hr>
                            <p class="mb-0">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    This fee has been marked as paid. Changes to amount or due date may affect financial records.
                                </small>
                            </p>
                        </div>
                        {% endif %}

                        <!-- Warning for overdue fees -->
                        {% if (fee.status or 'pending') == 'overdue' %}
                        <div class="alert alert-warning mb-5">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle me-2"></i>Overdue Fee
                            </h6>
                            <p class="mb-0">
                                This fee is overdue. Consider updating the due date or recording payment if received.
                            </p>
                        </div>
                        {% endif %}

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Update Fee Record
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
    .info-display {
        background: var(--gray-50);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
    }

    .info-field {
        margin-bottom: var(--space-4);
    }

    .info-field:last-child {
        margin-bottom: 0;
    }

    .info-field label {
        display: block;
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--gray-600);
        margin-bottom: var(--space-1);
    }

    .info-value {
        font-size: var(--text-base);
        font-weight: 500;
        color: var(--gray-900);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
{% endblock %}
