{% extends "layout.html" %}

{% block title %}Financial Management - EduManage{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background-color: #f8fafc; min-height: 100vh;">
    <!-- Fee Management Header -->
    <div class="bg-primary text-white rounded-3 p-4 mb-4 shadow-sm">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="h2 mb-2">
                    <i class="bi bi-currency-dollar me-2"></i>
                    Financial Management
                </h1>
                <p class="mb-0 opacity-75">Comprehensive fee management system for tracking student payments, dues, and financial records.</p>
            </div>
            <div class="col-lg-4 mt-3 mt-lg-0 text-lg-end">
                {% if current_user.role == 'admin' %}
                <a href="{{ url_for('fees.add_fee') }}" class="btn btn-light btn-lg shadow-sm">
                    <i class="bi bi-plus-circle me-2"></i>Add Fee Record
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-3 shadow-sm p-4 mb-4">
        <div class="d-flex align-items-center mb-3">
            <h5 class="mb-0 text-dark">
                <i class="bi bi-funnel text-primary me-2"></i>Filter Records
            </h5>
            <span class="badge bg-light text-dark ms-auto">{{ fees|length or 0 }} records found</span>
        </div>
        <form method="GET" class="row g-3">
            <div class="col-lg-3 col-md-6">
                <label for="search" class="form-label fw-semibold text-dark">
                    <i class="bi bi-search me-1"></i>Search Records
                </label>
                <div class="position-relative">
                    <i class="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                    <input type="text" class="form-control ps-5" id="search" name="search"
                           value="{{ search }}" placeholder="Search student or fee type">
                </div>
            </div>
                <div class="col-lg-2 col-md-6">
                    <label for="status" class="form-label fw-semibold text-dark">
                        <i class="bi bi-check-circle me-1"></i>Payment Status
                    </label>
                    <select class="form-select border-2" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="paid" {{ 'selected' if status_filter == 'paid' }}>✅ Paid</option>
                        <option value="pending" {{ 'selected' if status_filter == 'pending' }}>⏳ Pending</option>
                        <option value="overdue" {{ 'selected' if status_filter == 'overdue' }}>❌ Overdue</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="fee_type" class="form-label fw-semibold text-dark">
                        <i class="bi bi-tag me-1"></i>Fee Type
                    </label>
                    <select class="form-select border-2" id="fee_type" name="fee_type">
                        <option value="">All Types</option>
                        <option value="Tuition" {{ 'selected' if fee_type_filter == 'Tuition' }}>📚 Tuition</option>
                        <option value="Library" {{ 'selected' if fee_type_filter == 'Library' }}>📖 Library</option>
                        <option value="Lab" {{ 'selected' if fee_type_filter == 'Lab' }}>🔬 Lab</option>
                        <option value="Sports" {{ 'selected' if fee_type_filter == 'Sports' }}>⚽ Sports</option>
                        <option value="Transport" {{ 'selected' if fee_type_filter == 'Transport' }}>🚌 Transport</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="class" class="form-label fw-semibold text-dark">
                        <i class="bi bi-mortarboard me-1"></i>Class
                    </label>
                    <select class="form-select border-2" id="class" name="class">
                        <option value="">All Classes</option>
                        <option value="Grade 10" {{ 'selected' if class_filter == 'Grade 10' }}>Grade 10</option>
                        <option value="Grade 11" {{ 'selected' if class_filter == 'Grade 11' }}>Grade 11</option>
                        <option value="Grade 12" {{ 'selected' if class_filter == 'Grade 12' }}>Grade 12</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-12 d-flex align-items-end gap-2">
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="bi bi-funnel me-2"></i>Apply Filters
                    </button>
                    <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary px-4">
                        <i class="bi bi-arrow-clockwise me-2"></i>Reset
                    </a>
                </div>
            </form>
        </div>

    <!-- Fee Records Table -->
    <div class="bg-white rounded-3 shadow-sm">
        <div class="bg-light rounded-top-3 p-4 border-bottom">
            <div class="d-flex align-items-center justify-content-between">
                <h2 class="mb-0 text-dark">
                    <i class="bi bi-table text-primary me-2"></i>
                    Fee Records
                </h2>
                <span class="badge bg-primary fs-6 px-3 py-2">{{ fees|length or 0 }} Total Records</span>
            </div>
        </div>

        {% if fees %}
        <div class="table-responsive">
            <table class="table table-hover mb-0 align-middle">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-person me-2"></i>Student
                                </th>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-tag me-2"></i>Fee Type
                                </th>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-currency-dollar me-2"></i>Amount
                                </th>
                                <th class="fw-semibold border-0 py-3 d-none d-md-table-cell">
                                    <i class="bi bi-calendar me-2"></i>Due Date
                                </th>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-check-circle me-2"></i>Status
                                </th>
                                <th class="fw-semibold border-0 py-3 d-none d-lg-table-cell">
                                    <i class="bi bi-calendar-check me-2"></i>Payment Date
                                </th>
                                {% if current_user.role == 'admin' %}
                                <th class="fw-semibold border-0 py-3 text-center d-none d-xl-table-cell">
                                    <i class="bi bi-gear me-2"></i>Actions
                                </th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for fee in fees %}
                            <tr class="border-bottom">
                                <td class="py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-primary text-white d-flex align-items-center justify-content-center rounded-circle"
                                                 style="width: 40px; height: 40px; font-weight: 600; font-size: 0.9rem;">
                                                {{ fee.student_name[0] if fee.student_name else 'S' }}
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-semibold text-dark mb-1">{{ fee.student_name or 'John Smith' }}</div>
                                            <small class="text-muted">
                                                <i class="bi bi-person-badge me-1"></i>ID: {{ fee.student_id or 'STU001' }}
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    {% set fee_type = fee.fee_type or 'Tuition' %}
                                    {% if fee_type == 'Tuition' %}
                                        <span class="badge bg-primary-subtle text-primary px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% elif fee_type == 'Library' %}
                                        <span class="badge bg-info-subtle text-info px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% elif fee_type == 'Lab' %}
                                        <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% elif fee_type == 'Sports' %}
                                        <span class="badge bg-warning-subtle text-warning px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% elif fee_type == 'Transport' %}
                                        <span class="badge bg-secondary-subtle text-secondary px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% elif fee_type == 'Exam' %}
                                        <span class="badge bg-dark-subtle text-dark px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% elif fee_type == 'Hostel' %}
                                        <span class="badge bg-secondary-subtle text-secondary px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }} Fee
                                        </span>
                                    {% else %}
                                        <span class="badge bg-light text-muted px-3 py-2 rounded-pill fw-semibold">
                                            💰 {{ fee_type }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="py-3">
                                    <div class="text-center">
                                        <div class="fw-bold text-primary fs-5">${{ "{:,.0f}".format(fee.amount or 2500.00) }}</div>
                                        <small class="text-muted">Amount Due</small>
                                    </div>
                                </td>
                                <td class="py-3 d-none d-md-table-cell">
                                    <div class="text-center">
                                        <div class="fw-semibold text-dark mb-1">{{ fee.due_date|date if fee.due_date else 'Dec 31, 2023' }}</div>
                                        {% if fee.display_status == 'overdue' and fee.days_overdue > 0 %}
                                        <small class="text-danger fw-semibold">{{ fee.days_overdue|int }} days overdue</small>
                                        {% else %}
                                        <small class="text-success">On time</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="py-3">
                                    {% set status = fee.status or 'pending' %}
                                    {% set display_status = fee.display_status or 'pending' %}
                                    <div class="text-center">
                                        {% if status == 'paid' %}
                                            <span class="badge bg-success text-white px-3 py-2 rounded-pill fw-semibold">
                                                ✅ PAID
                                            </span>
                                        {% elif display_status == 'overdue' %}
                                            <span class="badge bg-danger text-white px-3 py-2 rounded-pill fw-semibold">
                                                ❌ OVERDUE
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark px-3 py-2 rounded-pill fw-semibold">
                                                ⏳ PENDING
                                            </span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="py-3 d-none d-lg-table-cell">
                                    {% if fee.paid_date %}
                                        <div class="text-center">
                                            <div class="fw-semibold text-success mb-1">{{ fee.paid_date|date }}</div>
                                            {% if fee.payment_method %}
                                            <span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill small">
                                                {% if fee.payment_method == 'Cash' %}💵{% elif fee.payment_method == 'Bank Transfer' %}🏦{% elif fee.payment_method == 'Credit Card' %}💳{% else %}💰{% endif %}
                                                {{ fee.payment_method }}
                                            </span>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <div class="text-center">
                                            <span class="badge bg-light text-muted px-3 py-2 rounded-pill">
                                                ❌ Not Paid
                                            </span>
                                        </div>
                                    {% endif %}
                                </td>
                                {% if current_user.role == 'admin' %}
                                <td class="py-3 d-none d-xl-table-cell">
                                    <div class="d-flex gap-1 justify-content-center">
                                        {% if (fee.status or 'pending') != 'paid' %}
                                        <a href="{{ url_for('fees.record_payment', fee_id=fee.id) if fee.id else '#' }}"
                                           class="btn btn-sm btn-success rounded-pill" title="Record Payment">
                                            <i class="bi bi-check-circle"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('fees.edit_fee', fee_id=fee.id) if fee.id else '#' }}"
                                           class="btn btn-sm btn-outline-primary rounded-pill" title="Edit Fee">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('fees.delete_fee', fee_id=fee.id) if fee.id else '#' }}"
                                           class="btn btn-sm btn-outline-danger rounded-pill" title="Delete Fee"
                                           onclick="return confirm('Are you sure you want to delete this fee record?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5 px-4">
                <div class="empty-state-icon mb-4">
                    <i class="bi bi-currency-dollar text-muted" style="font-size: 5rem; opacity: 0.5;"></i>
                </div>
                <h3 class="text-dark mb-3 fw-bold">No fee records found</h3>
                <p class="text-muted mb-4 fs-5">Try adjusting your search criteria or add new fee records to get started.</p>
                {% if current_user.role == 'admin' %}
                <a href="{{ url_for('fees.add_fee') }}" class="btn btn-primary btn-lg px-4 py-3 rounded-pill shadow-sm">
                    <i class="bi bi-plus-circle me-2"></i>Add First Fee Record
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
