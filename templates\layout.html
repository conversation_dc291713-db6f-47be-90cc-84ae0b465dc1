<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Student Result Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        {% if current_user %}
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-primary me-2 px-3 py-2">{{ current_user.role.title() }}</span>
                            <span class="d-none d-sm-inline">Welcome, {{ current_user.full_name }}</span>
                        </div>
                        {% else %}
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <i class="bi bi-clock me-2"></i>
                            <span class="small">24/7 Support Available</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('dashboard.dashboard') if current_user else url_for('auth.login') }}">
                    <div class="brand-logo">
                        <i class="bi bi-mortarboard-fill"></i>
                    </div>
                    <div class="brand-text">
                        <h4>EduManage</h4>
                        <small>Student Management System</small>
                    </div>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    {% if current_user %}
                    <ul class="navbar-nav mx-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard.dashboard') }}">
                                <i class="bi bi-house-door"></i>Dashboard
                            </a>
                        </li>

                        {% if current_user.role == 'admin' %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-people"></i>User Management
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('users.users_list') }}">
                                    <i class="bi bi-person-lines-fill text-primary"></i>All Users
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('users.students_list') }}">
                                    <i class="bi bi-mortarboard text-success"></i>Students
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('users.add_user') }}">
                                    <i class="bi bi-person-plus text-info"></i>Add User
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('users.teacher_assignments') }}">
                                    <i class="bi bi-person-badge text-warning"></i>Teacher Assignments
                                </a></li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        {% if current_user.role in ['admin', 'teacher'] %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-graph-up"></i>Academic Results
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('results.results_list') }}">
                                    <i class="bi bi-list-ul text-primary"></i>All Results
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('results.add_result') }}">
                                    <i class="bi bi-plus-circle text-success"></i>Add Result
                                </a></li>
                            </ul>
                        </li>
                        {% endif %}

                        {% if current_user.role == 'admin' %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-currency-dollar"></i>Financial Management
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('fees.fees_list') }}">
                                    <i class="bi bi-list-check text-primary"></i>All Fees
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('fees.add_fee') }}">
                                    <i class="bi bi-plus-circle text-success"></i>Add Fee
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('fees.financial_reports') }}">
                                    <i class="bi bi-bar-chart text-info"></i>Financial Reports
                                </a></li>
                            </ul>
                        </li>
                        {% elif current_user.role == 'teacher' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('fees.fees_list') }}">
                                <i class="bi bi-eye"></i>View Fees
                            </a>
                        </li>
                        {% elif current_user.role == 'student' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('results.results_list') }}">
                                <i class="bi bi-graph-up"></i>My Results
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('fees.fees_list') }}">
                                <i class="bi bi-currency-dollar"></i>My Fees
                            </a>
                        </li>
                        {% endif %}
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle user-dropdown d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar me-3">
                                    <i class="bi bi-person-circle fs-5"></i>
                                </div>
                                <div class="user-info d-none d-lg-block">
                                    <div class="fw-semibold">{{ current_user.full_name }}</div>
                                    <small>{{ current_user.role.title() }}</small>
                                </div>
                                <i class="bi bi-chevron-down ms-2 d-none d-lg-inline" style="font-size: 0.75rem;"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <div class="dropdown-item-text d-lg-none">
                                        <div class="fw-semibold text-dark">{{ current_user.full_name }}</div>
                                        <small class="text-muted">{{ current_user.role.title() }}</small>
                                    </div>
                                </li>
                                <li class="d-lg-none"><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-person text-primary"></i>My Profile
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-gear text-secondary"></i>Settings
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-bell text-info"></i>Notifications
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right text-danger"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                    {% else %}
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">
                                <i class="bi bi-box-arrow-in-right"></i>Login
                            </a>
                        </li>
                    </ul>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 bg-light">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container mt-3">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        <div class="container-fluid py-4">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="footer-brand">
                        <div class="brand-logo">
                            <i class="bi bi-mortarboard-fill"></i>
                        </div>
                        <div class="brand-text">
                            <h5>EduManage</h5>
                            <small>Student Management System</small>
                        </div>
                    </div>
                    <p class="footer-description">Comprehensive solution for managing student records, academic results, and financial transactions with modern technology.</p>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="footer-heading">Quick Links</h6>
                    <ul class="footer-links">
                        <li><a href="{{ url_for('dashboard.dashboard') if current_user else '#' }}">Dashboard</a></li>
                        <li><a href="{{ url_for('results.results_list') if current_user else '#' }}">Results</a></li>
                        <li><a href="{{ url_for('fees.fees_list') if current_user else '#' }}">Fees</a></li>
                        <li><a href="#">Reports</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="footer-heading">Contact Info</h6>
                    <ul class="footer-contact">
                        <li>
                            <i class="bi bi-geo-alt"></i>
                            <span>123 Education Street, Learning City, LC 12345</span>
                        </li>
                        <li>
                            <i class="bi bi-telephone"></i>
                            <span>+****************</span>
                        </li>
                        <li>
                            <i class="bi bi-envelope"></i>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-12 mb-4">
                    <h6 class="footer-heading">Follow Us</h6>
                    <div class="footer-social">
                        <a href="#" class="social-link">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="bi bi-linkedin"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="bi bi-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p>&copy; 2024 EduManage. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-legal">
                            <a href="#">Privacy Policy</a>
                            <a href="#">Terms of Service</a>
                            <a href="#">Support</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}?v=3.0"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
