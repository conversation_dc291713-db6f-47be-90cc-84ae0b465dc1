{% extends "layout.html" %}

{% block title %}Add New User - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Add User Header -->
        <div class="page-header bg-gradient-primary">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title text-white mb-2">
                            <i class="bi bi-person-plus-fill me-3"></i>
                            Add New User
                        </h1>
                        <p class="page-subtitle text-white-75 mb-0">Create a new user account with appropriate role-based access and permissions for the education management system.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions text-end">
                            <a href="{{ url_for('users.users_list') }}" class="btn btn-light btn-lg shadow-sm">
                                <i class="bi bi-arrow-left me-2"></i>Back to Users
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add User Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="content-section bg-white rounded-3 shadow-sm">
                    <div class="section-header bg-light rounded-top-3 p-4 border-bottom">
                        <h2 class="section-title mb-0 text-dark">
                            <i class="bi bi-person-badge text-primary me-2"></i>
                            User Account Information
                        </h2>
                        <p class="text-muted mb-0 mt-2">Create a new user account with role-based permissions</p>
                    </div>

                    <div class="p-4">
                        <form method="POST" class="user-form" id="userForm">
                            <!-- Basic Information -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="username" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-person text-primary me-2"></i>Username *
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">👤</span>
                                        <input type="text" class="form-control border-2" id="username" name="username"
                                               placeholder="Enter unique username" required minlength="3">
                                    </div>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Username must be at least 3 characters long.
                                    </div>
                                    <small class="text-muted">Will be auto-generated from full name if left empty</small>
                                </div>

                                <div class="col-md-6">
                                    <label for="role" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-shield text-primary me-2"></i>User Role *
                                    </label>
                                    <select class="form-select form-select-lg border-2" id="role" name="role" required onchange="toggleStudentFields()">
                                        <option value="">Choose user role...</option>
                                        <option value="student">👨‍🎓 Student</option>
                                        <option value="teacher">👨‍🏫 Teacher</option>
                                        <option value="admin">👨‍💼 Administrator</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select a role.
                                    </div>
                                </div>
                            </div>

                            <!-- Password and Full Name -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="password" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-lock text-primary me-2"></i>Password *
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">🔒</span>
                                        <input type="password" class="form-control border-2" id="password" name="password"
                                               placeholder="Enter secure password" required minlength="6">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="bi bi-eye" id="password-toggle-icon"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Password must be at least 6 characters long.
                                    </div>
                                    <small class="text-muted">Minimum 6 characters required</small>
                                </div>

                                <div class="col-md-6">
                                    <label for="full_name" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-person-badge text-primary me-2"></i>Full Name *
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">👨‍💼</span>
                                        <input type="text" class="form-control border-2" id="full_name" name="full_name"
                                               placeholder="Enter full name" required>
                                    </div>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Full name is required.
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="email" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-envelope text-primary me-2"></i>Email Address *
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">📧</span>
                                        <input type="email" class="form-control border-2" id="email" name="email"
                                               placeholder="<EMAIL>" required>
                                    </div>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please enter a valid email address.
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="phone" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-telephone text-primary me-2"></i>Phone Number
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">📱</span>
                                        <input type="tel" class="form-control border-2" id="phone" name="phone"
                                               placeholder="+****************">
                                    </div>
                                    <small class="text-muted">Optional contact number</small>
                                </div>
                            </div>

                        <div class="form-floating mb-5">
                            <textarea class="form-control" id="address" name="address"
                                      placeholder="Address" style="height: 100px;"></textarea>
                            <label for="address">
                                <i class="bi bi-geo-alt me-2"></i>Address
                            </label>
                        </div>

                        <!-- Student-specific fields -->
                        <div id="student-fields" class="student-fields" style="display: none;">
                            <h6 class="text-primary mb-4">
                                <i class="bi bi-mortarboard me-2"></i>Student Information
                            </h6>

                            <div class="row g-4">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="student_id" name="student_id"
                                               placeholder="Student ID">
                                        <label for="student_id">Student ID *</label>
                                        <div class="invalid-feedback">
                                            Student ID is required for students.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="class_name" name="class_name"
                                               placeholder="Class">
                                        <label for="class_name">Class *</label>
                                        <div class="invalid-feedback">
                                            Class is required for students.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="section" name="section"
                                               placeholder="Section">
                                        <label for="section">Section</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-4 mt-2">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="parent_name" name="parent_name"
                                               placeholder="Parent Name">
                                        <label for="parent_name">Parent/Guardian Name</label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" class="form-control" id="parent_phone" name="parent_phone"
                                               placeholder="Parent Phone">
                                        <label for="parent_phone">Parent/Guardian Phone</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{{ url_for('users.users_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
    .student-fields {
        background: var(--primary-50);
        border: 2px solid var(--primary-200);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        margin-top: var(--space-6);
    }

    .form-floating {
        margin-bottom: var(--space-4);
    }

    .form-floating > .form-control {
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-4) var(--space-4);
        font-size: var(--text-base);
        transition: all var(--transition-base);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        background: white;
    }

    .form-floating > .form-control:hover,
    .form-floating > .form-select:hover {
        border-color: var(--gray-400);
    }

    .form-floating > .form-control::placeholder {
        color: var(--gray-500);
        opacity: 1;
    }

    .form-floating > label {
        color: var(--gray-600);
        font-weight: 500;
        font-size: var(--text-sm);
        transition: all var(--transition-base);
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select:focus ~ label,
    .form-floating > .form-select:not(:placeholder-shown) ~ label {
        color: var(--primary-600);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleStudentFields() {
    const roleSelect = document.getElementById('role');
    const studentFields = document.getElementById('student-fields');
    const studentId = document.getElementById('student_id');
    const className = document.getElementById('class_name');

    if (roleSelect.value === 'student') {
        studentFields.style.display = 'block';
        studentId.required = true;
        className.required = true;
    } else {
        studentFields.style.display = 'none';
        studentId.required = false;
        className.required = false;
    }
}

function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle-icon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'bi bi-eye-slash';
    } else {
        passwordField.type = 'password';
        toggleIcon.className = 'bi bi-eye';
    }
}

// Auto-generate student ID
document.getElementById('role').addEventListener('change', function() {
    if (this.value === 'student') {
        const timestamp = Date.now().toString().slice(-4);
        document.getElementById('student_id').value = 'STU' + timestamp;
    }
});

// Auto-generate username from full name
document.getElementById('full_name').addEventListener('blur', function() {
    const usernameField = document.getElementById('username');
    if (!usernameField.value && this.value) {
        const username = this.value.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '')
            .substring(0, 15);
        usernameField.value = username;
    }
});

// BULLETPROOF USER FORM SUBMISSION - NO CONFLICTS
(function() {
    console.log('🎯 BULLETPROOF: User form JavaScript loading...');

    const form = document.getElementById('userForm');
    if (form) {
        console.log('✅ User form found:', form.action);

        form.addEventListener('submit', function(e) {
            console.log('🚀 BULLETPROOF: User form submitting to:', this.action);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Creating User...';
                submitBtn.disabled = true;
            }

            // NEVER prevent default - let it submit!
            console.log('✅ BULLETPROOF: Form will submit naturally');
        });
    } else {
        console.error('❌ User form not found!');
    }

    console.log('🎉 BULLETPROOF: User form ready for submission!');
})();
</script>
{% endblock %}
{% endblock %}
