<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Record Payment - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-success me-2 px-3 py-2">Payment</span>
                            <span class="d-none d-sm-inline">Record Payment</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/fees/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Fees
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="bi bi-check-circle me-3"></i>
                            Record Payment
                        </h1>
                        <p class="page-subtitle">Process and record payment for student fee with payment method and transaction details.</p>
                    </div>
                </div>
            </div>

            <!-- Fee Details -->
            <div class="card bg-light border-success mb-4">
                <div class="card-body">
                    <h6 class="text-success mb-3">
                        <i class="bi bi-info-circle me-2"></i>Fee Details
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Student:</strong> {{ fee.student_name or 'N/A' }}<br>
                            <strong>Fee Type:</strong> {{ fee.fee_type }}<br>
                            <strong>Total Amount:</strong> ${{ "%.2f"|format(fee.amount) }}
                        </div>
                        <div class="col-md-6">
                            <strong>Due Date:</strong> {{ fee.due_date.strftime('%Y-%m-%d') if fee.due_date else 'N/A' }}<br>
                            <strong>Paid Amount:</strong> ${{ "%.2f"|format(fee.paid_amount or 0) }}<br>
                            <strong>Outstanding:</strong> ${{ "%.2f"|format(fee.amount - (fee.paid_amount or 0)) }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-credit-card me-2"></i>
                                Payment Information
                            </h5>
                            <small class="opacity-75">Enter payment details and transaction information</small>
                        </div>
                        <div class="card-body p-4">
                            <!-- PURE HTML FORM - NO JAVASCRIPT -->
                            <form method="POST" action="/fees/payment/{{ fee.id }}">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="payment_amount" class="form-label">
                                            <i class="bi bi-currency-dollar me-1"></i>Payment Amount *
                                        </label>
                                        <input type="number" class="form-control" id="payment_amount" name="payment_amount" 
                                               required min="0" step="0.01" max="{{ fee.amount - (fee.paid_amount or 0) }}"
                                               value="{{ fee.amount - (fee.paid_amount or 0) }}">
                                        <div class="form-text">Maximum: ${{ "%.2f"|format(fee.amount - (fee.paid_amount or 0)) }}</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="payment_method" class="form-label">
                                            <i class="bi bi-credit-card me-1"></i>Payment Method *
                                        </label>
                                        <select class="form-select" id="payment_method" name="payment_method" required>
                                            <option value="">Select Method</option>
                                            <option value="cash">💵 Cash</option>
                                            <option value="card">💳 Credit/Debit Card</option>
                                            <option value="bank_transfer">🏦 Bank Transfer</option>
                                            <option value="check">📝 Check</option>
                                            <option value="online">🌐 Online Payment</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="payment_date" class="form-label">
                                            <i class="bi bi-calendar me-1"></i>Payment Date *
                                        </label>
                                        <input type="date" class="form-control" id="payment_date" name="payment_date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="transaction_id" class="form-label">
                                            <i class="bi bi-receipt me-1"></i>Transaction ID
                                        </label>
                                        <input type="text" class="form-control" id="transaction_id" name="transaction_id" 
                                               placeholder="Optional transaction reference">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_notes" class="form-label">
                                        <i class="bi bi-chat-text me-1"></i>Payment Notes
                                    </label>
                                    <textarea class="form-control" id="payment_notes" name="payment_notes" rows="3" 
                                              placeholder="Optional notes about this payment"></textarea>
                                </div>

                                <!-- Payment Summary -->
                                <div class="card bg-light mb-4">
                                    <div class="card-body">
                                        <h6 class="text-primary mb-3">Payment Summary</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Total Fee Amount:</strong><br>
                                                <strong>Previously Paid:</strong><br>
                                                <strong>Outstanding Amount:</strong><br>
                                                <strong class="text-success">Payment Amount:</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                ${{ "%.2f"|format(fee.amount) }}<br>
                                                ${{ "%.2f"|format(fee.paid_amount or 0) }}<br>
                                                ${{ "%.2f"|format(fee.amount - (fee.paid_amount or 0)) }}<br>
                                                <strong class="text-success" id="payment-display">$0.00</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-success btn-lg px-5">
                                        <i class="bi bi-check-circle me-2"></i>Record Payment
                                    </button>
                                    <a href="/fees/" class="btn btn-outline-secondary btn-lg ms-3 px-5">
                                        <i class="bi bi-arrow-left me-2"></i>Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 THEMED: Payment page loaded - pure HTML form with website theme');
        
        // Set default payment date to today
        document.addEventListener('DOMContentLoaded', function() {
            const paymentDateField = document.getElementById('payment_date');
            if (paymentDateField) {
                paymentDateField.valueAsDate = new Date();
            }
        });

        // Update payment display
        document.getElementById('payment_amount').addEventListener('input', function() {
            const amount = parseFloat(this.value) || 0;
            document.getElementById('payment-display').textContent = '$' + amount.toFixed(2);
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 THEMED: Payment form submitting to:', this.action);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Recording Payment...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ THEMED: Payment form ready for submission with website theme');
    </script>
</body>
</html>
