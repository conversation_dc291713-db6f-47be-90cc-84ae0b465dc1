#!/usr/bin/env python3
"""
Authentication Routes
Handles user login, logout, and session management
"""

from flask import Blueprint, render_template, request, redirect, url_for, session, flash
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
from models.db import get_db_connection, get_user_by_username

auth_bp = Blueprint('auth', __name__)

def login_required(f):
    """Decorator to require login for protected routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """Decorator to require admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        conn = get_db_connection()
        try:
            user = conn.execute('SELECT role FROM users WHERE id = ?', (session['user_id'],)).fetchone()

            if not user or user['role'] != 'admin':
                flash('Admin access required.', 'error')
                return redirect(url_for('dashboard.dashboard'))
        finally:
            conn.close()

        return f(*args, **kwargs)
    return decorated_function

def teacher_required(f):
    """Decorator to require teacher or admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        conn = get_db_connection()
        try:
            user = conn.execute('SELECT role FROM users WHERE id = ?', (session['user_id'],)).fetchone()

            if not user or user['role'] not in ['admin', 'teacher']:
                flash('Teacher or Admin access required.', 'error')
                return redirect(url_for('dashboard.dashboard'))
        finally:
            conn.close()

        return f(*args, **kwargs)
    return decorated_function

def fee_management_required(f):
    """Decorator to require admin role for fee management"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        conn = get_db_connection()
        user = conn.execute('SELECT role FROM users WHERE id = ?', (session['user_id'],)).fetchone()
        conn.close()

        if not user or user['role'] != 'admin':
            flash('Access denied. Only administrators can manage fees.', 'error')
            return redirect(url_for('dashboard.dashboard'))

        return f(*args, **kwargs)
    return decorated_function

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')

        print(f"Login attempt - Username: '{username}', Password length: {len(password)}")

        if not username or not password:
            flash('Please enter both username and password.', 'error')
            return render_template('login.html')

        try:
            user = get_user_by_username(username)
            print(f"User found: {user is not None}")

            if user:
                print(f"User details - ID: {user['id']}, Role: {user['role']}, Full name: {user['full_name']}")
                password_check = check_password_hash(user['password_hash'], password)
                print(f"Password check result: {password_check}")

                if password_check:
                    session['user_id'] = user['id']
                    session['username'] = user['username']
                    session['role'] = user['role']
                    session['full_name'] = user['full_name']

                    print(f"Session created for user: {user['full_name']}")
                    flash(f'Welcome back, {user["full_name"]}!', 'success')

                    # Redirect based on role
                    return redirect(url_for('dashboard.dashboard'))
                else:
                    flash('Invalid username or password.', 'error')
            else:
                flash('Invalid username or password.', 'error')
        except Exception as e:
            print(f"Login error: {e}")
            flash('An error occurred during login. Please try again.', 'error')

    return render_template('login.html')

@auth_bp.route('/logout')
def logout():
    """User logout"""
    username = session.get('full_name', 'User')
    session.clear()
    flash(f'Goodbye, {username}! You have been logged out.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration (admin only in production, open for demo)"""
    if request.method == 'POST':
        username = request.form['username'].strip()
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        role = request.form['role']
        full_name = request.form['full_name'].strip()
        email = request.form['email'].strip()
        phone = request.form.get('phone', '').strip()
        address = request.form.get('address', '').strip()
        
        # Validation
        errors = []
        
        if not username or len(username) < 3:
            errors.append('Username must be at least 3 characters long.')
        
        if not password or len(password) < 6:
            errors.append('Password must be at least 6 characters long.')
        
        if password != confirm_password:
            errors.append('Passwords do not match.')
        
        if not full_name:
            errors.append('Full name is required.')
        
        if not email or '@' not in email:
            errors.append('Valid email is required.')
        
        if role not in ['admin', 'teacher', 'student']:
            errors.append('Invalid role selected.')
        
        # Check for existing username/email
        conn = get_db_connection()
        existing_user = conn.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?', (username, email)
        ).fetchone()
        
        if existing_user:
            errors.append('Username or email already exists.')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            conn.close()
            return render_template('register.html')
        
        # Create user
        try:
            password_hash = generate_password_hash(password)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email, phone, address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, role, full_name, email, phone, address))
            
            user_id = cursor.lastrowid
            
            # If student, create student record
            if role == 'student':
                # Generate student ID
                student_count = conn.execute('SELECT COUNT(*) FROM students').fetchone()[0]
                student_id = f'STU{student_count + 1:03d}'
                
                cursor.execute('''
                    INSERT INTO students (user_id, student_id, class_name, section, admission_date, status)
                    VALUES (?, ?, ?, ?, DATE('now'), 'active')
                ''', (user_id, student_id, 'Not Assigned', 'Not Assigned'))
            
            conn.commit()
            flash('Registration successful! You can now log in.', 'success')
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            conn.rollback()
            flash(f'Registration failed: {str(e)}', 'error')
        finally:
            conn.close()
    
    return render_template('register.html')

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    if request.method == 'POST':
        current_password = request.form['current_password']
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']
        
        # Validation
        if not current_password or not new_password:
            flash('All fields are required.', 'error')
            return render_template('change_password.html')
        
        if len(new_password) < 6:
            flash('New password must be at least 6 characters long.', 'error')
            return render_template('change_password.html')
        
        if new_password != confirm_password:
            flash('New passwords do not match.', 'error')
            return render_template('change_password.html')
        
        # Verify current password
        conn = get_db_connection()
        user = conn.execute('SELECT password_hash FROM users WHERE id = ?', (session['user_id'],)).fetchone()
        
        if not user or not check_password_hash(user['password_hash'], current_password):
            flash('Current password is incorrect.', 'error')
            conn.close()
            return render_template('change_password.html')
        
        # Update password
        try:
            new_password_hash = generate_password_hash(new_password)
            conn.execute(
                'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                (new_password_hash, session['user_id'])
            )
            conn.commit()
            flash('Password changed successfully!', 'success')
            return redirect(url_for('profile'))
        except Exception as e:
            conn.rollback()
            flash(f'Failed to change password: {str(e)}', 'error')
        finally:
            conn.close()
    
    return render_template('change_password.html')
