{% extends "layout.html" %}

{% block title %}Profile - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Profile Header -->
        <div class="page-header">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title">
                            <i class="bi bi-person-circle"></i>
                            My Profile
                        </h1>
                        <p class="page-subtitle">View and manage your account information and settings.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions">
                            <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                                <i class="bi bi-key me-2"></i>Change Password
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="row g-4">
            <!-- Profile Information -->
            <div class="col-lg-8">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-person-badge"></i>
                            Personal Information
                        </h2>
                    </div>

                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Full Name</label>
                            <div class="form-control-plaintext fw-semibold fs-5">{{ user.full_name }}</div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Username</label>
                            <div class="form-control-plaintext">
                                <span class="status-badge active">@{{ user.username }}</span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Email Address</label>
                            <div class="form-control-plaintext">
                                <i class="bi bi-envelope me-2 text-muted"></i>
                                <a href="mailto:{{ user.email }}" class="text-decoration-none">{{ user.email }}</a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Phone Number</label>
                            <div class="form-control-plaintext">
                                {% if user.phone %}
                                    <i class="bi bi-telephone me-2 text-muted"></i>{{ user.phone }}
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-12">
                            <label class="form-label fw-semibold text-muted">Address</label>
                            <div class="form-control-plaintext">
                                {% if user.address %}
                                    <i class="bi bi-geo-alt me-2 text-muted"></i>{{ user.address }}
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Account Created</label>
                            <div class="form-control-plaintext">
                                <i class="bi bi-calendar me-2 text-muted"></i>{{ user.created_at|datetime }}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Last Updated</label>
                            <div class="form-control-plaintext">
                                <i class="bi bi-clock me-2 text-muted"></i>{{ user.updated_at|datetime }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role & Status -->
            <div class="col-lg-4">
                <div class="content-section text-center">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-shield-check"></i>
                            Account Status
                        </h2>
                    </div>

                    <div class="mb-4">
                        <div class="d-inline-flex align-items-center justify-content-center mb-3"
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); border-radius: 50%; color: white; font-size: 2rem;">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <h5 class="mb-3">{{ user.full_name }}</h5>
                        <span class="status-badge {{ 'admin' if user.role == 'admin' else 'teacher' if user.role == 'teacher' else 'student' }}">
                            <i class="bi bi-{{ 'shield-fill-check' if user.role == 'admin' else 'person-badge' if user.role == 'teacher' else 'mortarboard' }}"></i>
                            {{ user.role.title() }}
                        </span>
                    </div>

                    <div class="row g-3 text-start mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="text-muted">Account Status:</span>
                                <span class="status-badge active">Active</span>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="text-muted">User ID:</span>
                                <span class="fw-semibold">#{{ user.id }}</span>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center py-2">
                                <span class="text-muted">Member Since:</span>
                                <span class="fw-semibold">{{ user.created_at|date }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-lightning-charge"></i>
                            Quick Actions
                        </h2>
                    </div>

                    <div class="d-grid gap-3">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                            <i class="bi bi-key me-2"></i>Change Password
                        </a>

                        {% if user.role == 'admin' %}
                        <a href="{{ url_for('users.users_list') }}" class="btn btn-outline-info">
                            <i class="bi bi-people me-2"></i>Manage Users
                        </a>
                        {% endif %}

                        {% if user.role in ['admin', 'teacher'] %}
                        <a href="{{ url_for('results.add_result') }}" class="btn btn-outline-success">
                            <i class="bi bi-graph-up me-2"></i>Add Result
                        </a>
                        {% endif %}

                        <a href="{{ url_for('dashboard.dashboard') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
