// EduManage - Professional Student Management System JavaScript

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeComponents();
    initializeAnimations();
    initializeInteractions();
});

// Initialize all components
function initializeComponents() {
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            if (alert && alert.querySelector('.btn-close')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
}

// Initialize stable animations
function initializeAnimations() {
    // Simple fade in for cards - no conflicting transforms
    const cards = document.querySelectorAll('.stats-card, .action-card, .content-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transition = 'opacity 0.4s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
        }, index * 50);
    });

    // Animate progress bars with delay
    setTimeout(() => {
        const progressFills = document.querySelectorAll('.progress-fill');
        progressFills.forEach(fill => {
            const width = fill.style.width;
            if (width) {
                fill.style.width = '0%';
                setTimeout(() => {
                    fill.style.width = width;
                }, 300);
            }
        });
    }, 800);

    // Animate counters
    animateCounters();
}

// Initialize interactions
function initializeInteractions() {
    console.log('Initializing form interactions...');

    // SIMPLIFIED form validation - only prevent submission if form is invalid
    const forms = document.querySelectorAll('.needs-validation');
    console.log(`Found ${forms.length} forms with needs-validation class`);

    forms.forEach((form, index) => {
        // Skip login form to avoid interference
        if (form.classList.contains('login-form')) {
            console.log(`Skipping login form ${index}`);
            return;
        }

        console.log(`Setting up validation for form ${index}:`, form.action || 'same page');

        form.addEventListener('submit', function(event) {
            console.log(`🚀 Form ${index} submission attempt`);
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);
            console.log('Form validity:', form.checkValidity());

            // Only prevent submission if form is actually invalid
            if (!form.checkValidity()) {
                console.log(`❌ Form ${index} validation failed - preventing submission`);
                event.preventDefault();
                event.stopPropagation();

                // Focus on first invalid field
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    console.log('Focusing on invalid field:', firstInvalid.name || firstInvalid.id);
                    firstInvalid.focus();
                }

                // Add validation classes
                form.classList.add('was-validated');
            } else {
                console.log(`✅ Form ${index} validation passed - ALLOWING SUBMISSION`);
                // Form is valid - let it submit naturally
                // DO NOT call preventDefault() here
                form.classList.add('was-validated');
            }
        });
    });

    // Real-time search functionality
    const searchInputs = document.querySelectorAll('input[type="search"], input[name="search"]');
    searchInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                performSearch(this.value);
            }, 300);
        });
    });

    // Login form handling - minimal interference
    console.log('Login page JavaScript loaded');

    // Password toggle functionality
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const password = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (password && toggleIcon) {
                if (password.type === 'password') {
                    password.type = 'text';
                    toggleIcon.classList.remove('bi-eye');
                    toggleIcon.classList.add('bi-eye-slash');
                } else {
                    password.type = 'password';
                    toggleIcon.classList.remove('bi-eye-slash');
                    toggleIcon.classList.add('bi-eye');
                }
            }
        });
    }
}

// Animate counter numbers
function animateCounters() {
    const counters = document.querySelectorAll('.stats-number');
    
    counters.forEach(counter => {
        const text = counter.textContent;
        const target = parseInt(text.replace(/[^0-9]/g, '')) || 0;
        
        if (target > 0) {
            const duration = 1500;
            const step = target / (duration / 16);
            let current = 0;
            
            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    counter.textContent = text; // Restore original text with formatting
                    clearInterval(timer);
                } else {
                    const prefix = text.includes('$') ? '$' : '';
                    const suffix = text.includes('%') ? '%' : '';
                    counter.textContent = prefix + Math.floor(current) + suffix;
                }
            }, 16);
        }
    });
}

// Perform search functionality
function performSearch(query) {
    const tableRows = document.querySelectorAll('tbody tr');
    let visibleCount = 0;
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const matches = text.includes(query.toLowerCase());
        
        row.style.display = matches ? '' : 'none';
        if (matches) visibleCount++;
    });
    
    // Update results count
    updateResultsCount(visibleCount);
}

// Update results count
function updateResultsCount(count) {
    let countElement = document.querySelector('.results-count');
    if (!countElement) {
        countElement = document.createElement('div');
        countElement.className = 'results-count text-muted small mt-2';
        const table = document.querySelector('.table-responsive');
        if (table) {
            table.appendChild(countElement);
        }
    }
    if (countElement) {
        countElement.textContent = `Showing ${count} result${count !== 1 ? 's' : ''}`;
    }
}

// Utility functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Delete confirmation
function confirmDelete(message = 'Are you sure you want to delete this item?') {
    return confirm(message);
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Format date
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(new Date(date));
}

// Loading state for buttons
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Loading...';
        button.disabled = true;
    } else {
        button.innerHTML = button.dataset.originalText;
        button.disabled = false;
    }
}

// Smooth scroll to element
function scrollToElement(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Export functions for global use
window.EduManage = {
    showNotification,
    confirmDelete,
    formatCurrency,
    formatDate,
    setButtonLoading,
    scrollToElement,
    performSearch
};

// Add some global event listeners
document.addEventListener('click', function(e) {
    // Handle delete buttons
    if (e.target.matches('.btn-delete, .action-btn[onclick*="delete"]')) {
        if (!confirmDelete()) {
            e.preventDefault();
            return false;
        }
    }
    
    // Handle form submit buttons
    if (e.target.matches('button[type="submit"]')) {
        setButtonLoading(e.target, true);
        
        // Reset button after 5 seconds if form doesn't submit
        setTimeout(() => {
            setButtonLoading(e.target, false);
        }, 5000);
    }
});

// Handle form submissions - ONLY for UI feedback, NEVER prevent submission
document.addEventListener('submit', function(e) {
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');

    console.log('🌐 Global submit handler triggered');
    console.log('Form action:', form.action);
    console.log('Form method:', form.method);
    console.log('Form classes:', form.className);

    // Apply UI feedback for validated forms (but never prevent submission)
    if (form.classList.contains('needs-validation') && !form.classList.contains('login-form')) {
        console.log('📝 Applying UI feedback for validated form');

        if (submitBtn && form.checkValidity()) {
            console.log('✨ Form is valid, applying loading state');
            setButtonLoading(submitBtn, true);

            // Show success message after a short delay
            setTimeout(() => {
                showSuccessIndicator('Form submitted successfully! Processing...');
            }, 200);

            // Reset button after form submission completes or fails
            setTimeout(() => {
                setButtonLoading(submitBtn, false);
            }, 4000);
        }
    }

    // CRITICAL: Never prevent default here - always let the form submit
    console.log('🚀 Allowing form to submit naturally to:', form.action);
});

// Add success indicator function
function showSuccessIndicator(message) {
    // Remove any existing success indicators
    const existingIndicator = document.querySelector('.success-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Create success indicator
    const indicator = document.createElement('div');
    indicator.className = 'success-indicator alert alert-success alert-dismissible fade show position-fixed';
    indicator.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; animation: slideInRight 0.3s ease-out;';
    indicator.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(indicator);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (indicator && indicator.parentNode) {
            indicator.classList.add('fade');
            setTimeout(() => {
                if (indicator && indicator.parentNode) {
                    indicator.remove();
                }
            }, 300);
        }
    }, 4000);
}

// Add CSS animation for success indicator
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .success-indicator {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
        border-left: 4px solid #28a745;
    }
`;
document.head.appendChild(style);

console.log('EduManage JavaScript initialized successfully!');
