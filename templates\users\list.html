{% extends "layout.html" %}

{% block title %}User Management - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title">
                            <i class="bi bi-people-fill"></i>
                            User Management
                        </h1>
                        <p class="page-subtitle">Manage all system users, their roles, and permissions with comprehensive control.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions">
                            <a href="{{ url_for('users.add_user') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus-fill me-2"></i>Add New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="filter-section">
            <form method="GET" class="row g-4">
                <div class="col-md-4">
                    <label for="search" class="form-label fw-semibold">Search Users</label>
                    <div class="search-box">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ search }}" placeholder="Search by name, username, or email">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="role" class="form-label fw-semibold">Filter by Role</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">All Roles</option>
                        <option value="admin" {{ 'selected' if role_filter == 'admin' }}>Admin</option>
                        <option value="teacher" {{ 'selected' if role_filter == 'teacher' }}>Teacher</option>
                        <option value="student" {{ 'selected' if role_filter == 'student' }}>Student</option>
                    </select>
                </div>
                <div class="col-md-5 d-flex align-items-end gap-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-2"></i>Apply Filters
                    </button>
                    <a href="{{ url_for('users.users_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-2"></i>Reset
                    </a>
                    <small class="text-muted ms-auto">{{ total_users or users|length }} users found</small>
                </div>
            </form>
        </div>

        <!-- Users Table -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="bi bi-table"></i>
                    Users Directory
                </h2>
                <div class="section-actions">
                    <span class="badge bg-primary">{{ users|length }} Total Users</span>
                </div>
            </div>

            {% if users %}
            <div class="data-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="bi bi-person-fill"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">{{ user.full_name }}</div>
                                            <div class="text-muted small">@{{ user.username }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge {{ 'admin' if user.role == 'admin' else 'teacher' if user.role == 'teacher' else 'student' }}">
                                        <i class="bi bi-{{ 'shield-fill-check' if user.role == 'admin' else 'person-badge' if user.role == 'teacher' else 'mortarboard' }}"></i>
                                        {{ user.role.title() }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <i class="bi bi-envelope me-1 text-muted"></i>
                                        <a href="mailto:{{ user.email }}" class="text-decoration-none">{{ user.email }}</a>
                                    </div>
                                    {% if user.phone %}
                                    <div class="text-muted small">
                                        <i class="bi bi-telephone me-1"></i>{{ user.phone }}
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="status-badge active">
                                        <i class="bi bi-check-circle"></i>
                                        Active
                                    </span>
                                </td>
                                <td>
                                    <div class="text-muted small">{{ user.created_at|date if user.created_at else 'N/A' }}</div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('users.edit_user', user_id=user.id) }}"
                                           class="action-btn edit" title="Edit User">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        {% if user.id != current_user.id %}
                                        <a href="{{ url_for('users.delete_user', user_id=user.id) }}"
                                           class="action-btn delete" title="Delete User"
                                           onclick="return confirm('Are you sure you want to delete {{ user.full_name }}?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
            </div>

            <!-- Pagination -->
            {% if total_pages > 1 %}
            <div class="pagination-wrapper">
                <nav aria-label="Users pagination">
                    <ul class="pagination">
                        {% if page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users.users_list', page=page-1, search=search, role=role_filter) }}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for p in range(1, total_pages + 1) %}
                            {% if p == page %}
                            <li class="page-item active">
                                <span class="page-link">{{ p }}</span>
                            </li>
                            {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.users_list', page=p, search=search, role=role_filter) }}">{{ p }}</a>
                            </li>
                            {% elif p == 4 or p == total_pages - 3 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if page < total_pages %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users.users_list', page=page+1, search=search, role=role_filter) }}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-people" style="font-size: 4rem; color: var(--gray-400); margin-bottom: 2rem;"></i>
                <h3 style="color: var(--gray-600); margin-bottom: 1rem;">No users found</h3>
                <p style="color: var(--gray-500); margin-bottom: 2rem;">Try adjusting your search criteria or add a new user.</p>
                <a href="{{ url_for('users.add_user') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus-fill me-2"></i>Add First User
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
