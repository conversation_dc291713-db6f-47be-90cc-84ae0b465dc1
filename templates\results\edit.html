{% extends "layout.html" %}

{% block title %}Edit Result - Student Result Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="bi bi-pencil text-primary me-2"></i>
                    Edit Result
                </h1>
                <p class="text-muted mb-0">Update student examination result</p>
            </div>
            <div>
                <a href="{{ url_for('results.results_list') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Back to Results
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Current Result Info -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>Current Result Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <label class="form-label fw-semibold text-muted">Student</label>
                        <div class="form-control-plaintext fw-semibold">{{ result.student_name }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-semibold text-muted">Student ID</label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-primary">{{ result.student_id }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-semibold text-muted">Current Grade</label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-success fs-6">{{ result.grade or 'N/A' }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-semibold text-muted">Current Percentage</label>
                        <div class="form-control-plaintext fw-bold text-primary">
                            {{ "%.1f"|format((result.marks_obtained/result.total_marks)*100) }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pencil me-2"></i>Update Result
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <!-- Subject and Exam Type -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <label for="subject" class="form-label fw-semibold">
                                <i class="bi bi-book me-1 text-primary"></i>Subject *
                            </label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="{{ result.subject }}" placeholder="e.g., Mathematics" required>
                            <div class="invalid-feedback">
                                Subject is required.
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="exam_type" class="form-label fw-semibold">
                                <i class="bi bi-clipboard me-1 text-primary"></i>Exam Type *
                            </label>
                            <select class="form-select" id="exam_type" name="exam_type" required>
                                <option value="">Select Exam Type</option>
                                <option value="Mid Term" {{ 'selected' if result.exam_type == 'Mid Term' }}>Mid Term</option>
                                <option value="Final Term" {{ 'selected' if result.exam_type == 'Final Term' }}>Final Term</option>
                                <option value="Quiz" {{ 'selected' if result.exam_type == 'Quiz' }}>Quiz</option>
                                <option value="Assignment" {{ 'selected' if result.exam_type == 'Assignment' }}>Assignment</option>
                                <option value="Project" {{ 'selected' if result.exam_type == 'Project' }}>Project</option>
                                <option value="Practical" {{ 'selected' if result.exam_type == 'Practical' }}>Practical</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select exam type.
                            </div>
                        </div>
                    </div>
                    
                    <!-- Marks -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <label for="marks_obtained" class="form-label fw-semibold">
                                <i class="bi bi-award me-1 text-primary"></i>Marks Obtained *
                            </label>
                            <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" 
                                   value="{{ result.marks_obtained }}" placeholder="Enter marks obtained" required min="0" step="0.5">
                            <div class="invalid-feedback">
                                Please enter valid marks obtained.
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="total_marks" class="form-label fw-semibold">
                                <i class="bi bi-trophy me-1 text-primary"></i>Total Marks *
                            </label>
                            <input type="number" class="form-control" id="total_marks" name="total_marks" 
                                   value="{{ result.total_marks }}" placeholder="Enter total marks" required min="1" step="0.5">
                            <div class="invalid-feedback">
                                Please enter valid total marks.
                            </div>
                        </div>
                    </div>
                    
                    <!-- Exam Date -->
                    <div class="mb-4">
                        <label for="exam_date" class="form-label fw-semibold">
                            <i class="bi bi-calendar me-1 text-primary"></i>Exam Date *
                        </label>
                        <input type="date" class="form-control" id="exam_date" name="exam_date" 
                               value="{{ result.exam_date }}" required>
                        <div class="invalid-feedback">
                            Please select exam date.
                        </div>
                    </div>
                    
                    <!-- Grade Preview -->
                    <div class="mb-4" id="grade-preview">
                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Updated Grade Preview:</strong>
                                    <span id="percentage-display">{{ "%.1f"|format((result.marks_obtained/result.total_marks)*100) }}%</span>
                                </div>
                                <div>
                                    <span class="badge bg-primary fs-6" id="grade-display">{{ result.grade or '-' }}</span>
                                </div>
                            </div>
                            <div class="progress mt-2" style="height: 8px;">
                                <div class="progress-bar bg-success" id="grade-progress" role="progressbar" 
                                     style="width: {{ (result.marks_obtained/result.total_marks)*100 }}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('results.results_list') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Update Result
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-control-plaintext {
        padding: 0.375rem 0;
        margin-bottom: 0;
        font-size: 1rem;
        line-height: 1.5;
        color: #212529;
        background-color: transparent;
        border: solid transparent;
        border-width: 1px 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Calculate grade and percentage
function calculateGrade() {
    const marksObtained = parseFloat(document.getElementById('marks_obtained').value) || 0;
    const totalMarks = parseFloat(document.getElementById('total_marks').value) || 0;
    
    if (totalMarks > 0) {
        const percentage = (marksObtained / totalMarks) * 100;
        let grade = 'F';
        let progressColor = 'bg-danger';
        
        if (percentage >= 90) {
            grade = 'A+';
            progressColor = 'bg-success';
        } else if (percentage >= 80) {
            grade = 'A';
            progressColor = 'bg-success';
        } else if (percentage >= 70) {
            grade = 'B+';
            progressColor = 'bg-info';
        } else if (percentage >= 60) {
            grade = 'B';
            progressColor = 'bg-info';
        } else if (percentage >= 50) {
            grade = 'C+';
            progressColor = 'bg-warning';
        } else if (percentage >= 40) {
            grade = 'C';
            progressColor = 'bg-warning';
        } else if (percentage >= 33) {
            grade = 'D';
            progressColor = 'bg-danger';
        }
        
        // Update display
        document.getElementById('percentage-display').textContent = percentage.toFixed(1) + '%';
        document.getElementById('grade-display').textContent = grade;
        
        const progressBar = document.getElementById('grade-progress');
        progressBar.className = `progress-bar ${progressColor}`;
        progressBar.style.width = percentage + '%';
    }
}

// Add event listeners
document.getElementById('marks_obtained').addEventListener('input', calculateGrade);
document.getElementById('total_marks').addEventListener('input', calculateGrade);

// Validation
document.getElementById('marks_obtained').addEventListener('input', function() {
    const marksObtained = parseFloat(this.value) || 0;
    const totalMarks = parseFloat(document.getElementById('total_marks').value) || 0;
    
    if (totalMarks > 0 && marksObtained > totalMarks) {
        this.setCustomValidity('Marks obtained cannot exceed total marks');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('total_marks').addEventListener('input', function() {
    const marksObtained = parseFloat(document.getElementById('marks_obtained').value) || 0;
    const totalMarks = parseFloat(this.value) || 0;
    
    if (totalMarks > 0 && marksObtained > totalMarks) {
        document.getElementById('marks_obtained').setCustomValidity('Marks obtained cannot exceed total marks');
    } else {
        document.getElementById('marks_obtained').setCustomValidity('');
    }
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
