#!/usr/bin/env python3
"""
Results Management Routes
Handles CRUD operations for student results
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from routes.auth import login_required, teacher_required
from models.db import get_db_connection, calculate_grade, get_student_by_user_id, get_teacher_students, is_teacher_assigned_to_student

results_bp = Blueprint('results', __name__)

@results_bp.route('/debug')
@login_required
def debug_session():
    """Debug endpoint to check session and user data"""
    from flask import session
    debug_info = {
        'session_data': dict(session),
        'user_id': session.get('user_id'),
        'role': session.get('role'),
        'username': session.get('username')
    }
    
    # Get students list
    students = get_students_list()
    debug_info['students_count'] = len(students)
    debug_info['students'] = [dict(s) for s in students] if students else []
    
    return f"<pre>{debug_info}</pre>"

@results_bp.route('/')
@login_required
def results_list():
    """Display results based on user role"""
    user_role = session.get('role')
    user_id = session.get('user_id')
    
    if user_role == 'student':
        return student_results(user_id)
    else:
        return all_results()

def all_results():
    """Display all results (for teachers and admins)"""
    search = request.args.get('search', '')
    subject_filter = request.args.get('subject', '')
    class_filter = request.args.get('class', '')
    exam_type_filter = request.args.get('exam_type', '')
    page = int(request.args.get('page', 1))
    per_page = 15
    
    conn = get_db_connection()
    
    # Build query with filters
    query = '''
        SELECT r.*, s.student_id, s.class_name, s.section, u.full_name as student_name,
               t.full_name as teacher_name, (r.marks_obtained * 100.0 / r.total_marks) as percentage
        FROM results r
        JOIN students s ON r.student_id = s.id
        JOIN users u ON s.user_id = u.id
        LEFT JOIN users t ON r.teacher_id = t.id
        WHERE 1=1
    '''
    params = []
    
    # If teacher, only show their results
    if session.get('role') == 'teacher':
        query += ' AND r.teacher_id = ?'
        params.append(session.get('user_id'))
    
    if search:
        query += ' AND (u.full_name LIKE ? OR s.student_id LIKE ? OR r.subject LIKE ?)'
        search_param = f'%{search}%'
        params.extend([search_param, search_param, search_param])
    
    if subject_filter:
        query += ' AND r.subject = ?'
        params.append(subject_filter)
    
    if class_filter:
        query += ' AND s.class_name = ?'
        params.append(class_filter)
    
    if exam_type_filter:
        query += ' AND r.exam_type = ?'
        params.append(exam_type_filter)
    
    # Count total records
    count_query = query.replace(
        'SELECT r.*, s.student_id, s.class_name, s.section, u.full_name as student_name, t.full_name as teacher_name, (r.marks_obtained * 100.0 / r.total_marks) as percentage',
        'SELECT COUNT(*)'
    )
    total_results = conn.execute(count_query, params).fetchone()[0]
    
    # Add pagination and ordering
    query += ' ORDER BY r.exam_date DESC, u.full_name LIMIT ? OFFSET ?'
    params.extend([per_page, (page - 1) * per_page])
    
    results = conn.execute(query, params).fetchall()
    
    # Get filter options
    subjects = conn.execute('SELECT DISTINCT subject FROM results ORDER BY subject').fetchall()
    classes = conn.execute('SELECT DISTINCT class_name FROM students ORDER BY class_name').fetchall()
    exam_types = conn.execute('SELECT DISTINCT exam_type FROM results ORDER BY exam_type').fetchall()
    
    conn.close()
    
    # Calculate pagination info
    total_pages = (total_results + per_page - 1) // per_page
    
    return render_template('results/list.html',
                         results=results,
                         subjects=subjects,
                         classes=classes,
                         exam_types=exam_types,
                         search=search,
                         subject_filter=subject_filter,
                         class_filter=class_filter,
                         exam_type_filter=exam_type_filter,
                         page=page,
                         total_pages=total_pages,
                         total_results=total_results)

def student_results(user_id):
    """Display results for a specific student"""
    conn = get_db_connection()
    
    # Get student record
    student = get_student_by_user_id(user_id)
    if not student:
        flash('Student record not found.', 'error')
        return redirect(url_for('dashboard.dashboard'))
    
    # Get all results for this student
    results = conn.execute('''
        SELECT r.*, t.full_name as teacher_name, 
               (r.marks_obtained * 100.0 / r.total_marks) as percentage
        FROM results r
        LEFT JOIN users t ON r.teacher_id = t.id
        WHERE r.student_id = ?
        ORDER BY r.exam_date DESC, r.subject
    ''', (student['id'],)).fetchall()
    
    # Get subject-wise performance
    subject_performance = conn.execute('''
        SELECT subject,
               COUNT(*) as total_exams,
               AVG(marks_obtained * 100.0 / total_marks) as avg_percentage,
               MAX(marks_obtained * 100.0 / total_marks) as best_percentage,
               MIN(marks_obtained * 100.0 / total_marks) as lowest_percentage
        FROM results 
        WHERE student_id = ?
        GROUP BY subject
        ORDER BY avg_percentage DESC
    ''', (student['id'],)).fetchall()
    
    conn.close()
    
    return render_template('results/student_results.html',
                         student=student,
                         results=results,
                         subject_performance=subject_performance)

@results_bp.route('/add', methods=['GET', 'POST'])
@login_required
@teacher_required
def add_result():
    """Add new result"""
    conn = None
    try:
        if request.method == 'POST':
            # Debug: Print form data
            print("DEBUG - Add Result Form Data:")
            for key, value in request.form.items():
                print(f"  {key}: {value}")
            
            # Get form data with safe access
            student_id = request.form.get('student_id', '').strip()
            subject = request.form.get('subject', '').strip()
            exam_type = request.form.get('exam_type', '').strip()
            marks_obtained = request.form.get('marks_obtained', '').strip()
            total_marks = request.form.get('total_marks', '').strip()
            exam_date = request.form.get('exam_date', '').strip()
            
            print(f"DEBUG - Processed form data: student_id={student_id}, subject={subject}, exam_type={exam_type}")
            
            # Validation
            errors = []
            
            if not student_id:
                errors.append('Please select a student.')
            
            if not subject:
                errors.append('Subject is required.')
            
            if not exam_type:
                errors.append('Exam type is required.')
            
            try:
                marks_obtained_float = float(marks_obtained) if marks_obtained else 0
                total_marks_float = float(total_marks) if total_marks else 0
                
                if marks_obtained_float < 0 or total_marks_float <= 0:
                    errors.append('Marks must be positive numbers.')
                
                if marks_obtained_float > total_marks_float:
                    errors.append('Marks obtained cannot exceed total marks.')
            except ValueError:
                errors.append('Please enter valid numeric values for marks.')
                marks_obtained_float = 0
                total_marks_float = 0
            
            if not exam_date:
                errors.append('Exam date is required.')
            
            # Additional validation for teachers - ensure they can only add results for assigned students
            if session.get('role') == 'teacher' and student_id:
                if not is_teacher_assigned_to_student(session.get('user_id'), student_id):
                    errors.append('You are not assigned to teach this student.')

            if errors:
                print(f"DEBUG - Validation errors: {errors}")
                for error in errors:
                    flash(error, 'error')
                return render_template('results/add.html', students=get_students_list())
            
            # Calculate grade
            percentage = (marks_obtained_float / total_marks_float) * 100
            grade = calculate_grade(percentage)
            
            print(f"DEBUG - Calculated grade: {grade}, percentage: {percentage}")
            
            # Save result
            conn = get_db_connection()
            cursor = conn.execute('''
                INSERT INTO results (student_id, subject, exam_type, marks_obtained, 
                                   total_marks, grade, exam_date, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (student_id, subject, exam_type, marks_obtained_float, total_marks_float, 
                  grade, exam_date, session.get('user_id')))
            
            if cursor.rowcount > 0:
                conn.commit()
                print(f"DEBUG - Result added successfully with ID: {cursor.lastrowid}")
                flash('Result added successfully!', 'success')
                return redirect(url_for('results.results_list'))
            else:
                print("DEBUG - No rows affected during result insertion")
                flash('Failed to add result. Please try again.', 'error')
        
        students = get_students_list()
        return render_template('results/add.html', students=students)
        
    except Exception as e:
        print(f"DEBUG - Exception in add_result: {str(e)}")
        if conn:
            conn.rollback()
        flash(f'Failed to add result: {str(e)}', 'error')
        students = get_students_list()
        return render_template('results/add.html', students=students)
    finally:
        if conn:
            conn.close()

@results_bp.route('/edit/<int:result_id>', methods=['GET', 'POST'])
@login_required
@teacher_required
def edit_result(result_id):
    """Edit existing result"""
    conn = None
    try:
        conn = get_db_connection()
        
        # Get result data
        result = conn.execute('''
            SELECT r.*, s.student_id, u.full_name as student_name
            FROM results r
            JOIN students s ON r.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE r.id = ?
        ''', (result_id,)).fetchone()
        
        if not result:
            flash('Result not found.', 'error')
            return redirect(url_for('results.results_list'))
        
        # Check if teacher can edit this result
        if session.get('role') == 'teacher' and result['teacher_id'] != session.get('user_id'):
            flash('You can only edit results you have entered.', 'error')
            return redirect(url_for('results.results_list'))
        
        if request.method == 'POST':
            # Debug: Print form data
            print("DEBUG - Edit Result Form Data:")
            for key, value in request.form.items():
                print(f"  {key}: {value}")
            
            # Get form data with safe access
            subject = request.form.get('subject', '').strip()
            exam_type = request.form.get('exam_type', '').strip()
            marks_obtained = request.form.get('marks_obtained', '').strip()
            total_marks = request.form.get('total_marks', '').strip()
            exam_date = request.form.get('exam_date', '').strip()
            
            print(f"DEBUG - Processed form data: subject={subject}, exam_type={exam_type}")
            
            # Validation
            errors = []
            
            if not subject:
                errors.append('Subject is required.')
            
            if not exam_type:
                errors.append('Exam type is required.')
            
            try:
                marks_obtained_float = float(marks_obtained) if marks_obtained else 0
                total_marks_float = float(total_marks) if total_marks else 0
                
                if marks_obtained_float < 0 or total_marks_float <= 0:
                    errors.append('Marks must be positive numbers.')
                
                if marks_obtained_float > total_marks_float:
                    errors.append('Marks obtained cannot exceed total marks.')
            except ValueError:
                errors.append('Please enter valid numeric values for marks.')
                marks_obtained_float = 0
                total_marks_float = 0
            
            if not exam_date:
                errors.append('Exam date is required.')

            # Additional validation for teachers - ensure they can only edit results for assigned students
            if session.get('role') == 'teacher':
                if not is_teacher_assigned_to_student(session.get('user_id'), result['student_id']):
                    errors.append('You are not assigned to teach this student.')

            if errors:
                print(f"DEBUG - Validation errors: {errors}")
                for error in errors:
                    flash(error, 'error')
                return render_template('results/edit.html', result=result)
            
            # Calculate grade
            percentage = (marks_obtained_float / total_marks_float) * 100
            grade = calculate_grade(percentage)
            
            print(f"DEBUG - Calculated grade: {grade}, percentage: {percentage}")
            
            # Update result
            cursor = conn.execute('''
                UPDATE results 
                SET subject = ?, exam_type = ?, marks_obtained = ?, total_marks = ?, 
                    grade = ?, exam_date = ?
                WHERE id = ?
            ''', (subject, exam_type, marks_obtained_float, total_marks_float, grade, exam_date, result_id))
            
            if cursor.rowcount > 0:
                conn.commit()
                print(f"DEBUG - Result updated successfully for ID: {result_id}")
                flash('Result updated successfully!', 'success')
                return redirect(url_for('results.results_list'))
            else:
                print(f"DEBUG - No rows affected during result update for ID: {result_id}")
                flash('Failed to update result. Please try again.', 'error')
        
        return render_template('results/edit.html', result=result)
        
    except Exception as e:
        print(f"DEBUG - Exception in edit_result: {str(e)}")
        if conn:
            conn.rollback()
        flash(f'Failed to update result: {str(e)}', 'error')
        if 'result' in locals():
            return render_template('results/edit.html', result=result)
        else:
            return redirect(url_for('results.results_list'))
    finally:
        if conn:
            conn.close()

@results_bp.route('/delete/<int:result_id>', methods=['POST'])
@login_required
@teacher_required
def delete_result(result_id):
    """Delete result"""
    conn = get_db_connection()
    
    # Get result data
    result = conn.execute('SELECT * FROM results WHERE id = ?', (result_id,)).fetchone()
    if not result:
        flash('Result not found.', 'error')
        return redirect(url_for('results.results_list'))
    
    # Check if teacher can delete this result
    if session.get('role') == 'teacher' and result['teacher_id'] != session.get('user_id'):
        flash('You can only delete results you have entered.', 'error')
        return redirect(url_for('results.results_list'))
    
    try:
        conn.execute('DELETE FROM results WHERE id = ?', (result_id,))
        conn.commit()
        flash('Result deleted successfully!', 'success')
    except Exception as e:
        conn.rollback()
        flash(f'Failed to delete result: {str(e)}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('results.results_list'))

def get_students_list():
    """Get list of active students for dropdowns - filtered by teacher assignments"""
    from flask import session

    # If admin, return all students
    if session.get('role') == 'admin':
        conn = get_db_connection()
        students = conn.execute('''
            SELECT s.id, s.student_id, u.full_name, s.class_name, s.section
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE s.status = 'active'
            ORDER BY u.full_name
        ''').fetchall()
        conn.close()
        return students

    # If teacher, return only assigned students
    elif session.get('role') == 'teacher':
        return get_teacher_students(session.get('user_id'))

    # If student or other role, return empty list
    return []
