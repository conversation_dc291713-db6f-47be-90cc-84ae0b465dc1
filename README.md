# 🎓 Student Result Management System

A comprehensive full-stack web application for managing student results, user accounts, and financial records. Built with Flask, SQLite, and Bootstrap for a modern, responsive experience.

## 🚀 Features

### 🔐 Authentication & Role Management
- **Secure Login System**: Session-based authentication with password hashing
- **Role-Based Access Control**: Admin, Teacher, and Student roles with different permissions
- **User Registration**: Self-registration with role selection
- **Password Management**: Change password functionality

### 👥 User Management (Admin Only)
- **CRUD Operations**: Create, Read, Update, Delete users
- **Role Assignment**: Assign roles (Admin, Teacher, Student)
- **Student Profiles**: Extended student information with class, section, parent details
- **Search & Filter**: Find users by name, email, username, or role
- **Bulk Operations**: Manage multiple users efficiently

### 📊 Result Management (Admin & Teacher)
- **Grade Entry**: Add and manage student exam results
- **Subject-wise Results**: Track performance across multiple subjects
- **Grade Calculation**: Automatic grade assignment based on percentage
- **Result Analytics**: Performance statistics and trends
- **Export Options**: Generate reports and printable results

### 💰 Financial Management (Admin & Teacher)
- **Fee Management**: Track tuition, lab, library, and other fees
- **Payment Recording**: Mark payments as paid/pending/overdue
- **Financial Reports**: Revenue tracking and outstanding dues
- **Automated Alerts**: Color-coded overdue fee indicators
- **Payment History**: Complete transaction records

### 📱 Student Portal
- **Personal Dashboard**: Overview of academic performance and fees
- **Result Viewing**: Access to all exam results and grades
- **Fee Status**: View pending, paid, and overdue fees
- **Performance Analytics**: Subject-wise performance charts

## 🛠️ Technology Stack

- **Backend**: Python Flask 2.3.3
- **Database**: SQLite (built-in, no setup required)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5.3
- **Template Engine**: Jinja2
- **Security**: Werkzeug password hashing, session management
- **Icons**: Bootstrap Icons
- **Responsive Design**: Mobile-first approach

## 📁 Project Structure

```
student-result-system/
├── app.py                      # Main application file
├── requirements.txt            # Python dependencies
├── README.md                   # Project documentation
├── models/
│   └── db.py                   # Database models and utilities
├── routes/
│   ├── auth.py                 # Authentication routes
│   ├── dashboard.py            # Dashboard routes
│   ├── users.py                # User management routes
│   ├── results.py              # Result management routes
│   └── fees.py                 # Financial management routes
├── templates/
│   ├── layout.html             # Base template
│   ├── login.html              # Login page
│   ├── register.html           # Registration page
│   ├── dashboard/
│   │   ├── admin.html          # Admin dashboard
│   │   ├── teacher.html        # Teacher dashboard
│   │   └── student.html        # Student dashboard
│   ├── users/
│   │   ├── list.html           # User list
│   │   ├── add.html            # Add user form
│   │   └── edit.html           # Edit user form
│   ├── results/
│   │   ├── list.html           # Results list
│   │   ├── add.html            # Add result form
│   │   └── student_results.html # Student results view
│   └── fees/
│       ├── list.html           # Fees list
│       ├── add.html            # Add fee form
│       ├── payment.html        # Payment recording
│       └── reports.html        # Financial reports
├── static/
│   ├── css/
│   │   └── style.css           # Custom styles
│   └── js/
│       └── script.js           # Custom JavaScript
└── database/
    └── database.db             # SQLite database (auto-created)
```

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd student-result-system
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the application**
   - Open your browser and go to: `http://localhost:5000`
   - The database will be automatically created on first run

## 🔑 Default Login Credentials

The system comes with pre-configured demo accounts:

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Admin | `admin` | `admin123` | Full system access |
| Teacher | `teacher1` | `teacher123` | Results and fees management |
| Student | `student1` | `student123` | View personal results and fees |

## 📊 Database Schema

### Users Table
- `id` (Primary Key)
- `username` (Unique)
- `password_hash`
- `role` (admin/teacher/student)
- `full_name`
- `email` (Unique)
- `phone`
- `address`
- `created_at`
- `updated_at`

### Students Table
- `id` (Primary Key)
- `user_id` (Foreign Key to Users)
- `student_id` (Unique)
- `class_name`
- `section`
- `admission_date`
- `parent_name`
- `parent_phone`
- `status` (active/inactive/graduated)

### Results Table
- `id` (Primary Key)
- `student_id` (Foreign Key to Students)
- `subject`
- `exam_type`
- `marks_obtained`
- `total_marks`
- `grade`
- `exam_date`
- `teacher_id` (Foreign Key to Users)
- `created_at`

### Financial Records Table
- `id` (Primary Key)
- `student_id` (Foreign Key to Students)
- `fee_type`
- `amount`
- `due_date`
- `paid_date`
- `status` (pending/paid/overdue/partial)
- `payment_method`
- `remarks`
- `created_at`

## 🎨 Features Showcase

### 📱 Responsive Design
- Mobile-first responsive layout
- Touch-friendly interface
- Optimized for tablets and smartphones

### 🔍 Advanced Search & Filtering
- Real-time search across all modules
- Multi-criteria filtering
- Sortable tables with pagination

### 📈 Analytics & Reports
- Performance dashboards
- Financial reports
- Grade distribution charts
- Trend analysis

### 🎯 User Experience
- Intuitive navigation
- Flash messages for user feedback
- Form validation (client & server-side)
- Loading indicators
- Confirmation dialogs

## 🔧 Configuration

### Environment Variables (Optional)
Create a `.env` file for production settings:
```
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///database/database.db
FLASK_ENV=production
```

### Database Configuration
The application uses SQLite by default. To use a different database:
1. Install the appropriate database driver
2. Update the `DATABASE` configuration in `app.py`
3. Modify connection settings in `models/db.py`

## 🚀 Deployment

### Local Development
```bash
python app.py
```

### Production Deployment
```bash
# Using Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app

# Using Docker (create Dockerfile)
docker build -t student-management .
docker run -p 5000:5000 student-management
```

## 🧪 Testing

Run the test suite:
```bash
pytest tests/
```

## 📝 API Endpoints

### Authentication
- `GET /auth/login` - Login page
- `POST /auth/login` - Process login
- `GET /auth/logout` - Logout user
- `GET /auth/register` - Registration page
- `POST /auth/register` - Process registration

### Dashboard
- `GET /dashboard/` - Role-based dashboard

### User Management (Admin only)
- `GET /users/` - List all users
- `GET /users/add` - Add user form
- `POST /users/add` - Create new user
- `GET /users/edit/<id>` - Edit user form
- `POST /users/edit/<id>` - Update user
- `POST /users/delete/<id>` - Delete user

### Results Management
- `GET /results/` - List results
- `GET /results/add` - Add result form
- `POST /results/add` - Create result
- `GET /results/edit/<id>` - Edit result form
- `POST /results/edit/<id>` - Update result
- `POST /results/delete/<id>` - Delete result

### Financial Management
- `GET /fees/` - List fees
- `GET /fees/add` - Add fee form
- `POST /fees/add` - Create fee record
- `GET /fees/pay/<id>` - Payment form
- `POST /fees/pay/<id>` - Record payment
- `GET /fees/reports` - Financial reports

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Flask framework and community
- Bootstrap for responsive UI components
- Bootstrap Icons for beautiful iconography
- SQLite for reliable database functionality

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Built with ❤️ for educational institutions worldwide**
