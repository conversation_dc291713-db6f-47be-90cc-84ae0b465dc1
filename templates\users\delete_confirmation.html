<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete User Confirmation - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-danger me-2 px-3 py-2">Delete User</span>
                            <span class="d-none d-sm-inline">{{ user.full_name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/users/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Users
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title text-danger">
                            <i class="bi bi-exclamation-triangle me-3"></i>
                            Delete User Confirmation
                        </h1>
                        <p class="page-subtitle">This action will permanently delete the user and all related records. This cannot be undone.</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Warning Alert -->
                    <div class="alert alert-danger border-danger" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-3 fs-2"></i>
                            <div>
                                <h5 class="alert-heading mb-2">⚠️ PERMANENT DELETION WARNING</h5>
                                <p class="mb-0">You are about to permanently delete this user and ALL related records. This action cannot be undone!</p>
                            </div>
                        </div>
                    </div>

                    <!-- User Information -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person-x me-2"></i>
                                User to be Deleted
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">User Details</h6>
                                    <p><strong>Full Name:</strong> {{ user.full_name }}</p>
                                    <p><strong>Username:</strong> {{ user.username }}</p>
                                    <p><strong>Role:</strong> 
                                        <span class="badge bg-{{ 'primary' if user.role == 'admin' else 'info' if user.role == 'teacher' else 'success' }}">
                                            {{ user.role.title() }}
                                        </span>
                                    </p>
                                    <p><strong>Email:</strong> {{ user.email }}</p>
                                    <p><strong>Phone:</strong> {{ user.phone or 'Not provided' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">Related Records to be Deleted</h6>
                                    
                                    {% if user.role == 'student' and related_data %}
                                        <div class="alert alert-warning">
                                            <p><strong>Student ID:</strong> {{ related_data.student_id }}</p>
                                            <p><strong>Class:</strong> {{ related_data.class_name }}</p>
                                            <hr>
                                            <p><i class="bi bi-clipboard-data me-2"></i><strong>Results:</strong> {{ related_data.results }} records</p>
                                            <p><i class="bi bi-currency-dollar me-2"></i><strong>Fee Records:</strong> {{ related_data.fees }} records</p>
                                            <p><i class="bi bi-credit-card me-2"></i><strong>Payment Records:</strong> {{ related_data.payments }} records</p>
                                        </div>
                                    {% elif user.role == 'teacher' and related_data %}
                                        <div class="alert alert-info">
                                            <p><i class="bi bi-book me-2"></i><strong>Class Assignments:</strong> {{ related_data.assignments }} records</p>
                                            <p><i class="bi bi-clipboard-data me-2"></i><strong>Results Created:</strong> {{ related_data.results_created }} records</p>
                                            <small class="text-muted">Note: Results will be kept but teacher reference will be removed</small>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-secondary">
                                            <p><i class="bi bi-info-circle me-2"></i>No related records found</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Confirmation Form -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 text-danger">
                                <i class="bi bi-shield-exclamation me-2"></i>
                                Confirm Deletion
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6 class="alert-heading">What will be deleted:</h6>
                                <ul class="mb-0">
                                    <li>User account: <strong>{{ user.full_name }}</strong></li>
                                    {% if user.role == 'student' and related_data %}
                                        <li>Student record and profile</li>
                                        {% if related_data.results > 0 %}
                                            <li>{{ related_data.results }} examination results</li>
                                        {% endif %}
                                        {% if related_data.fees > 0 %}
                                            <li>{{ related_data.fees }} fee records</li>
                                        {% endif %}
                                        {% if related_data.payments > 0 %}
                                            <li>{{ related_data.payments }} payment records</li>
                                        {% endif %}
                                    {% elif user.role == 'teacher' and related_data %}
                                        {% if related_data.assignments > 0 %}
                                            <li>{{ related_data.assignments }} class assignments</li>
                                        {% endif %}
                                        {% if related_data.results_created > 0 %}
                                            <li>Teacher reference from {{ related_data.results_created }} results (results will be kept)</li>
                                        {% endif %}
                                    {% endif %}
                                </ul>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <form method="POST" action="/users/delete/{{ user.id }}" style="display: inline;">
                                        <button type="submit" class="btn btn-danger btn-lg w-100" 
                                                onclick="return confirm('Are you absolutely sure? This action cannot be undone!')">
                                            <i class="bi bi-trash me-2"></i>
                                            Yes, Delete Permanently
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <a href="/users/" class="btn btn-secondary btn-lg w-100">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        Cancel - Keep User
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('🎯 THEMED: Delete confirmation page loaded');
        
        // Add extra confirmation for dangerous action
        document.querySelector('form').addEventListener('submit', function(e) {
            const confirmed = confirm('⚠️ FINAL WARNING: This will permanently delete the user and all related records. Are you absolutely sure?');
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Deleting...';
                submitBtn.disabled = true;
            }
        });
    </script>
</body>
</html>
