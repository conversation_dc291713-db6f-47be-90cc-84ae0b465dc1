{% extends "layout.html" %}

{% block title %}Student Dashboard - EduManage{% endblock %}

{% block extra_css %}
<style>
    /* Student Dashboard - Clean Bootstrap-Compatible Styles */
    .student-dashboard {
        background-color: #f8fafc;
        min-height: 100vh;
        padding: 1rem 0;
    }

    .dashboard-welcome {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        color: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(79, 70, 229, 0.2);
    }

    .dashboard-stats .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        overflow: hidden;
    }

    .dashboard-stats .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .stat-card-header {
        height: 4px;
        border-radius: 12px 12px 0 0;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .dashboard-content .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .result-item {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.75rem;
        border-left: 4px solid #e2e8f0;
        transition: all 0.2s ease;
    }

    .result-item:hover {
        background: #f1f5f9;
        border-left-color: #4f46e5;
        transform: translateX(2px);
    }

    .fee-summary-card {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1.25rem;
        text-align: center;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
    }

    .fee-summary-card:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 3rem;
        opacity: 0.5;
        margin-bottom: 1rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .dashboard-welcome {
            padding: 1.5rem;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
    }

</style>
{% endblock %}

{% block content %}
<div class="student-dashboard">
    <div class="container-fluid">
        <!-- Welcome Header -->
        <div class="dashboard-welcome">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="h2 mb-2">
                        <i class="bi bi-mortarboard me-2"></i>
                        Welcome Back, {{ current_user.full_name }}!
                    </h1>
                    <p class="mb-0 opacity-90">
                        Here's your academic overview and recent activity.
                    </p>
                </div>
                <div class="col-lg-4 mt-3 mt-lg-0">
                    <div class="d-flex flex-column gap-2">
                        <div class="info-badge">
                            <i class="bi bi-person-badge"></i>
                            Student ID: {{ current_user.student_id or 'STU001' }}
                        </div>
                        <div class="info-badge">
                            <i class="bi bi-calendar3"></i>
                            {{ moment().format('MMM DD, YYYY') if moment else 'Today' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="dashboard-stats mb-4">
            <div class="row g-3">
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="stat-card-header bg-success"></div>
                        <div class="card-body text-center">
                            <div class="stat-icon bg-success mx-auto">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h3 class="fw-bold text-dark mb-1">{{ "%.1f"|format(stats.overall_percentage or 85.5) }}%</h3>
                            <p class="text-muted small mb-0 text-uppercase fw-semibold">Overall Average</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="stat-card-header bg-primary"></div>
                        <div class="card-body text-center">
                            <div class="stat-icon bg-primary mx-auto">
                                <i class="bi bi-journal-text"></i>
                            </div>
                            <h3 class="fw-bold text-dark mb-1">{{ stats.total_exams or 12 }}</h3>
                            <p class="text-muted small mb-0 text-uppercase fw-semibold">Total Exams</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="stat-card-header" style="background-color: #8b5cf6;"></div>
                        <div class="card-body text-center">
                            <div class="stat-icon mx-auto" style="background-color: #8b5cf6;">
                                <i class="bi bi-book"></i>
                            </div>
                            <h3 class="fw-bold text-dark mb-1">{{ stats.total_subjects or 8 }}</h3>
                            <p class="text-muted small mb-0 text-uppercase fw-semibold">Active Subjects</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="stat-card-header bg-warning"></div>
                        <div class="card-body text-center">
                            <div class="stat-icon bg-warning mx-auto">
                                <i class="bi bi-trophy"></i>
                            </div>
                            <h3 class="fw-bold text-dark mb-1">A+</h3>
                            <p class="text-muted small mb-0 text-uppercase fw-semibold">Best Grade</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Main Content -->
        <div class="dashboard-content">
            <div class="row g-4">
                <!-- Recent Results -->
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success me-3">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-1">Recent Results</h5>
                                    <p class="text-muted small mb-0">Your latest academic achievements</p>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if recent_results %}
                                {% for result in recent_results %}
                                <div class="result-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="fw-semibold mb-1">{{ result.subject }}</h6>
                                            <small class="text-muted">{{ result.exam_type }} • {{ result.exam_date }}</small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-primary mb-1">{{ result.marks_obtained }}/{{ result.total_marks }}</div>
                                            <span class="badge {{ 'bg-success' if result.percentage >= 80 else 'bg-warning' if result.percentage >= 60 else 'bg-danger' }}">
                                                {{ "%.1f"|format(result.percentage) }}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="empty-state">
                                    <i class="bi bi-graph-up d-block"></i>
                                    <h6 class="text-muted">No results yet</h6>
                                    <p class="text-muted small">Your exam results will appear here</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <!-- Financial Status -->
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning me-3">
                                    <i class="bi bi-wallet2"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-1">Financial Status</h5>
                                    <p class="text-muted small mb-0">Your payment overview and dues</p>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Fee Summary -->
                            <div class="row g-3 mb-4">
                                <div class="col-4">
                                    <div class="fee-summary-card">
                                        <i class="bi bi-check-circle-fill text-success d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <div class="fw-bold fs-5 text-success">${{ "%.0f"|format(stats.paid_fees or 2500) }}</div>
                                        <small class="text-muted fw-semibold">Paid</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="fee-summary-card">
                                        <i class="bi bi-clock-fill text-warning d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <div class="fw-bold fs-5 text-warning">${{ "%.0f"|format(stats.pending_fees or 800) }}</div>
                                        <small class="text-muted fw-semibold">Pending</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="fee-summary-card">
                                        <i class="bi bi-exclamation-triangle-fill text-danger d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <div class="fw-bold fs-5 text-danger">${{ "%.0f"|format(stats.overdue_fees or 200) }}</div>
                                        <small class="text-muted fw-semibold">Overdue</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Fee Records -->
                            {% if recent_fees %}
                                {% for fee in recent_fees %}
                                <div class="result-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="fw-semibold mb-1">{{ fee.fee_type }}</h6>
                                            <small class="text-muted">Due: {{ fee.due_date }}</small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-primary mb-1">${{ "%.0f"|format(fee.amount) }}</div>
                                            <span class="badge bg-success">{{ fee.status.title() }}</span>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="empty-state">
                                    <i class="bi bi-wallet2 d-block"></i>
                                    <h6 class="text-muted">No fee records</h6>
                                    <p class="text-muted small">Your payment information will appear here</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
