<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Fee Record - EduManage</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #218838, #1e7e34);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="bi bi-plus-circle-fill me-3"></i>
                            Add Fee Record
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">Create a new fee record for a student</p>
                    </div>
                    <div class="card-body p-5">
                        <!-- PURE HTML FORM - NO JAVASCRIPT -->
                        <form method="POST" action="/fees/add">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="student_id" class="form-label">
                                        <i class="bi bi-person me-1"></i>Select Student *
                                    </label>
                                    <select class="form-select" id="student_id" name="student_id" required>
                                        <option value="">Choose Student</option>
                                        {% for student in students %}
                                            <option value="{{ student.id }}">{{ student.full_name }} ({{ student.username }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="fee_type" class="form-label">
                                        <i class="bi bi-tag me-1"></i>Fee Type *
                                    </label>
                                    <select class="form-select" id="fee_type" name="fee_type" required>
                                        <option value="">Select Fee Type</option>
                                        <option value="Tuition">📚 Tuition Fee</option>
                                        <option value="Library">📖 Library Fee</option>
                                        <option value="Laboratory">🔬 Laboratory Fee</option>
                                        <option value="Sports">⚽ Sports Fee</option>
                                        <option value="Transport">🚌 Transport Fee</option>
                                        <option value="Examination">📝 Examination Fee</option>
                                        <option value="Other">📋 Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">
                                        <i class="bi bi-currency-dollar me-1"></i>Amount *
                                    </label>
                                    <input type="number" class="form-control" id="amount" name="amount" 
                                           required min="0" step="0.01" placeholder="0.00">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="due_date" class="form-label">
                                        <i class="bi bi-calendar me-1"></i>Due Date *
                                    </label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>Remarks
                                </label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Optional notes about this fee"></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-plus-circle me-2"></i>Add Fee Record
                                </button>
                                <a href="/fees/" class="btn btn-outline-secondary btn-lg ms-3">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 STANDALONE: Fees add page loaded - pure HTML form');
        
        // Set default due date to next month
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
            const dueDateField = document.getElementById('due_date');
            if (dueDateField) {
                dueDateField.valueAsDate = nextMonth;
            }
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 STANDALONE: Fees form submitting to:', this.action);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Adding Fee...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ STANDALONE: Fees form ready for submission');
    </script>
</body>
</html>
