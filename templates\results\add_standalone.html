<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Result - EduManage</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(45deg, #17a2b8, #138496);
        }
        .form-control:focus, .form-select:focus {
            border-color: #17a2b8;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #17a2b8, #138496);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #138496, #117a8b);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="bi bi-plus-circle-fill me-3"></i>
                            Add Student Result
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">Record a new examination result for a student</p>
                    </div>
                    <div class="card-body p-5">
                        <!-- PURE HTML FORM - NO JAVASCRIPT -->
                        <form method="POST" action="/results/add">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="student_id" class="form-label">
                                        <i class="bi bi-person me-1"></i>Select Student *
                                    </label>
                                    <select class="form-select" id="student_id" name="student_id" required>
                                        <option value="">Choose Student</option>
                                        {% for student in students %}
                                            <option value="{{ student.id }}">{{ student.full_name }} ({{ student.username }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">
                                        <i class="bi bi-book me-1"></i>Subject *
                                    </label>
                                    <input type="text" class="form-control" id="subject" name="subject" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="exam_type" class="form-label">
                                        <i class="bi bi-clipboard-check me-1"></i>Exam Type *
                                    </label>
                                    <select class="form-select" id="exam_type" name="exam_type" required>
                                        <option value="">Select Type</option>
                                        <option value="Midterm">📝 Midterm</option>
                                        <option value="Final">🎓 Final</option>
                                        <option value="Quiz">❓ Quiz</option>
                                        <option value="Assignment">📋 Assignment</option>
                                        <option value="Project">🔬 Project</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="exam_date" class="form-label">
                                        <i class="bi bi-calendar me-1"></i>Exam Date *
                                    </label>
                                    <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="marks_obtained" class="form-label">
                                        <i class="bi bi-award me-1"></i>Marks Obtained *
                                    </label>
                                    <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" 
                                           required min="0" step="0.01">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="total_marks" class="form-label">
                                        <i class="bi bi-trophy me-1"></i>Total Marks *
                                    </label>
                                    <input type="number" class="form-control" id="total_marks" name="total_marks" 
                                           required min="1" step="0.01">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>Remarks
                                </label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Optional comments about the result"></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-plus-circle me-2"></i>Add Result
                                </button>
                                <a href="/results/" class="btn btn-outline-secondary btn-lg ms-3">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 STANDALONE: Results add page loaded - pure HTML form');
        
        // Set default exam date to today
        document.addEventListener('DOMContentLoaded', function() {
            const examDateField = document.getElementById('exam_date');
            if (examDateField) {
                examDateField.valueAsDate = new Date();
            }
        });

        // Simple marks validation
        document.getElementById('marks_obtained').addEventListener('input', function() {
            const marksObtained = parseFloat(this.value) || 0;
            const totalMarks = parseFloat(document.getElementById('total_marks').value) || 0;
            
            if (totalMarks > 0 && marksObtained > totalMarks) {
                this.setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                this.setCustomValidity('');
            }
        });

        document.getElementById('total_marks').addEventListener('input', function() {
            const marksObtained = parseFloat(document.getElementById('marks_obtained').value) || 0;
            const totalMarks = parseFloat(this.value) || 0;
            
            if (totalMarks > 0 && marksObtained > totalMarks) {
                document.getElementById('marks_obtained').setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                document.getElementById('marks_obtained').setCustomValidity('');
            }
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 STANDALONE: Results form submitting to:', this.action);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Adding Result...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ STANDALONE: Results form ready for submission');
    </script>
</body>
</html>
