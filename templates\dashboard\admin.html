{% extends "layout.html" %}

{% block title %}Admin Dashboard - EduManage{% endblock %}

{% block extra_css %}
<style>
    /* Admin Dashboard - Professional UI Design */
    .dashboard-page {
        background: var(--gray-50);
        min-height: calc(100vh - 160px);
        padding: var(--space-8) 0 var(--space-12);
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 var(--space-6);
    }

    /* Header Section - Improved Spacing & Alignment */
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
        color: white;
        border-radius: var(--radius-2xl);
        padding: var(--space-12) var(--space-10);
        margin-bottom: var(--space-12);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-lg);
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }

    .dashboard-header-content {
        position: relative;
        z-index: 2;
    }

    .dashboard-title {
        font-size: var(--text-4xl);
        font-weight: 700;
        margin-bottom: var(--space-3);
        display: flex;
        align-items: center;
        gap: var(--space-4);
        line-height: 1.2;
    }

    .dashboard-title i {
        font-size: var(--text-3xl);
        opacity: 0.9;
    }

    .dashboard-subtitle {
        font-size: var(--text-lg);
        opacity: 0.9;
        margin: 0;
        line-height: 1.5;
        font-weight: 400;
    }

    .dashboard-meta {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: var(--space-4);
        margin-top: var(--space-8);
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-3) var(--space-5);
        background: rgba(255, 255, 255, 0.15);
        border-radius: var(--radius-xl);
        font-size: var(--text-sm);
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .meta-item i {
        font-size: var(--text-base);
        opacity: 0.9;
    }

    /* Statistics Cards - Perfect Grid & Spacing */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-8);
        margin-bottom: var(--space-16);
    }

    .stats-card {
        background: white;
        border-radius: var(--radius-2xl);
        padding: var(--space-10);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    .stats-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-300);
    }

    .stats-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--space-8);
    }

    .stats-icon {
        width: 64px;
        height: 64px;
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-2xl);
        color: white;
        box-shadow: var(--shadow-md);
        flex-shrink: 0;
    }

    .stats-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .stats-number {
        font-size: var(--text-4xl);
        font-weight: 800;
        color: var(--gray-900);
        margin-bottom: var(--space-2);
        line-height: 1;
        letter-spacing: -0.02em;
    }

    .stats-label {
        color: var(--gray-600);
        font-weight: 600;
        font-size: var(--text-base);
        margin-bottom: var(--space-6);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-change {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
        font-weight: 600;
        padding: var(--space-2) var(--space-4);
        border-radius: var(--radius-lg);
        align-self: flex-start;
    }

    .stats-change.positive {
        background: var(--success-100);
        color: var(--success-700);
        border: 1px solid var(--success-200);
    }

    .stats-change.negative {
        background: var(--danger-100);
        color: var(--danger-700);
        border: 1px solid var(--danger-200);
    }

    .stats-change i {
        font-size: var(--text-sm);
    }

    /* Quick Actions - Enhanced Grid & Interactions */
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: var(--space-8);
        margin-bottom: var(--space-16);
    }

    .action-card {
        background: white;
        border-radius: var(--radius-2xl);
        padding: var(--space-10);
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid var(--gray-200);
        text-decoration: none;
        color: inherit;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-sm);
        position: relative;
        overflow: hidden;
        min-height: 180px;
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, var(--primary-50) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow: var(--shadow-xl);
        color: inherit;
        text-decoration: none;
        border-color: var(--primary-400);
    }

    .action-card:hover::before {
        opacity: 1;
    }

    .action-card:hover .action-icon {
        transform: scale(1.1);
    }

    .action-icon {
        width: 64px;
        height: 64px;
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-2xl);
        margin-bottom: var(--space-6);
        color: white;
        box-shadow: var(--shadow-lg);
        transition: transform 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .action-title {
        font-size: var(--text-lg);
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        position: relative;
        z-index: 2;
    }

    .action-description {
        font-size: var(--text-sm);
        color: var(--gray-600);
        line-height: 1.6;
        margin: 0;
        position: relative;
        z-index: 2;
        text-align: center;
    }

    /* Content Grid - Enhanced Layout & Spacing */
    .content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--space-10);
        margin-top: var(--space-4);
    }

    .content-card {
        background: white;
        border-radius: var(--radius-2xl);
        padding: var(--space-10);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
        height: fit-content;
    }

    .content-card:hover {
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-200);
    }

    .content-header {
        display: flex;
        align-items: center;
        gap: var(--space-4);
        margin-bottom: var(--space-8);
        padding-bottom: var(--space-4);
        border-bottom: 2px solid var(--gray-100);
    }

    .content-header i {
        font-size: var(--text-2xl);
    }

    .content-title {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
        letter-spacing: -0.01em;
    }

    .activity-list {
        display: flex;
        flex-direction: column;
        gap: var(--space-5);
    }

    .activity-item {
        padding: var(--space-6);
        border-radius: var(--radius-xl);
        background: var(--gray-50);
        border-left: 5px solid var(--primary-600);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .activity-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, var(--primary-50) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .activity-item:hover {
        background: white;
        transform: translateX(8px);
        box-shadow: var(--shadow-md);
        border-left-color: var(--primary-500);
    }

    .activity-item:hover::before {
        opacity: 1;
    }

    .activity-content {
        display: flex;
        align-items: flex-start;
        gap: var(--space-4);
        position: relative;
        z-index: 2;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-xl);
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-base);
        flex-shrink: 0;
        box-shadow: var(--shadow-sm);
    }

    .activity-text {
        flex: 1;
        min-width: 0;
    }

    .activity-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--space-2);
        line-height: 1.4;
    }

    .activity-description {
        font-size: var(--text-sm);
        color: var(--gray-600);
        margin-bottom: var(--space-2);
        line-height: 1.5;
    }

    .activity-time {
        font-size: var(--text-xs);
        color: var(--gray-500);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .overview-list {
        display: flex;
        flex-direction: column;
        gap: var(--space-5);
    }

    .overview-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .overview-label {
        font-size: var(--text-sm);
        font-weight: 500;
        color: var(--gray-700);
    }

    .overview-value {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--primary-600);
    }

    .progress-bar-container {
        margin-top: var(--space-2);
    }

    .progress-bar {
        height: 6px;
        background: var(--gray-200);
        border-radius: var(--radius-sm);
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        border-radius: var(--radius-sm);
        transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    /* Overview Section Styles */
    .overview-list {
        display: flex;
        flex-direction: column;
        gap: var(--space-8);
    }

    .overview-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--space-4);
    }

    .overview-label {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: var(--space-3);
    }

    .overview-value {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--primary-600);
        flex-shrink: 0;
        min-width: 60px;
        text-align: right;
    }

    /* Responsive Design - Mobile First Approach */
    @media (max-width: 1199.98px) {
        .dashboard-container {
            padding: 0 var(--space-4);
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
        }

        .actions-grid {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--space-6);
        }
    }

    @media (max-width: 991.98px) {
        .dashboard-header {
            padding: var(--space-8) var(--space-6);
            margin-bottom: var(--space-10);
        }

        .dashboard-title {
            font-size: var(--text-3xl);
        }

        .dashboard-meta {
            flex-direction: row;
            justify-content: center;
            align-items: center;
            gap: var(--space-3);
            margin-top: var(--space-6);
        }

        .content-grid {
            grid-template-columns: 1fr;
            gap: var(--space-8);
        }

        .stats-grid {
            margin-bottom: var(--space-12);
        }

        .actions-grid {
            margin-bottom: var(--space-12);
        }
    }

    @media (max-width: 767.98px) {
        .dashboard-page {
            padding: var(--space-6) 0 var(--space-10);
        }

        .dashboard-container {
            padding: 0 var(--space-3);
        }

        .dashboard-header {
            padding: var(--space-6) var(--space-4);
            margin-bottom: var(--space-8);
        }

        .dashboard-title {
            font-size: var(--text-2xl);
            gap: var(--space-3);
        }

        .dashboard-title i {
            font-size: var(--text-2xl);
        }

        .dashboard-subtitle {
            font-size: var(--text-base);
        }

        .stats-grid,
        .actions-grid {
            grid-template-columns: 1fr;
            gap: var(--space-6);
            margin-bottom: var(--space-10);
        }

        .stats-card,
        .action-card {
            padding: var(--space-8);
        }

        .action-card {
            min-height: 160px;
        }

        .content-card {
            padding: var(--space-8);
        }

        .content-title {
            font-size: var(--text-xl);
        }

        .activity-item {
            padding: var(--space-5);
        }

        .activity-content {
            gap: var(--space-3);
        }

        .activity-icon {
            width: 36px;
            height: 36px;
        }
    }

    @media (max-width: 575.98px) {
        .dashboard-page {
            padding: var(--space-4) 0 var(--space-8);
        }

        .dashboard-header {
            padding: var(--space-5) var(--space-3);
            margin-bottom: var(--space-6);
        }

        .dashboard-title {
            font-size: var(--text-xl);
            flex-direction: column;
            text-align: center;
            gap: var(--space-2);
        }

        .dashboard-subtitle {
            font-size: var(--text-sm);
            text-align: center;
        }

        .dashboard-meta {
            justify-content: center;
            flex-wrap: wrap;
        }

        .meta-item {
            padding: var(--space-2) var(--space-3);
            font-size: var(--text-xs);
        }

        .stats-card,
        .action-card,
        .content-card {
            padding: var(--space-6);
        }

        .stats-number {
            font-size: var(--text-3xl);
        }

        .action-card {
            min-height: 140px;
        }

        .action-icon {
            width: 56px;
            height: 56px;
            font-size: var(--text-xl);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-page">
    <div class="dashboard-container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="dashboard-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="dashboard-title">
                            <i class="bi bi-speedometer2"></i>
                            Admin Dashboard
                        </h1>
                        <p class="dashboard-subtitle">Welcome back, {{ current_user.full_name }}! Here's your comprehensive system overview and management center.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="dashboard-meta">
                            <div class="meta-item">
                                <i class="bi bi-calendar-check"></i>
                                <span>{{ moment().format('MMM DD, YYYY') if moment else 'Today' }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="bi bi-clock"></i>
                                <span>{{ moment().format('h:mm A') if moment else 'Dashboard' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--primary-500), var(--primary-600));">
                        <i class="bi bi-people-fill"></i>
                    </div>
                </div>
                <div class="stats-content">
                    <div class="stats-number">{{ stats.total_users or 247 }}</div>
                    <div class="stats-label">Total Users</div>
                    <div class="stats-change positive">
                        <i class="bi bi-arrow-up"></i>
                        <span>+12% this month</span>
                    </div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--success-500), var(--success-600));">
                        <i class="bi bi-mortarboard-fill"></i>
                    </div>
                </div>
                <div class="stats-content">
                    <div class="stats-number">{{ stats.active_students or 189 }}</div>
                    <div class="stats-label">Active Students</div>
                    <div class="stats-change positive">
                        <i class="bi bi-arrow-up"></i>
                        <span>+8% this month</span>
                    </div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--warning-500), var(--warning-600));">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                </div>
                <div class="stats-content">
                    <div class="stats-number">${{ "{:,}".format(stats.total_revenue or 45280) }}</div>
                    <div class="stats-label">Total Revenue</div>
                    <div class="stats-change positive">
                        <i class="bi bi-arrow-up"></i>
                        <span>+15% this month</span>
                    </div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--danger-500), var(--danger-600));">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                    </div>
                </div>
                <div class="stats-content">
                    <div class="stats-number">${{ "{:,}".format(stats.pending_dues or 8750) }}</div>
                    <div class="stats-label">Pending Dues</div>
                    <div class="stats-change negative">
                        <i class="bi bi-arrow-down"></i>
                        <span>-5% this month</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="actions-grid">
            <a href="{{ url_for('users.add_user') }}" class="action-card">
                <div class="action-icon" style="background: var(--primary-600);">
                    <i class="bi bi-person-plus-fill"></i>
                </div>
                <h3 class="action-title">Add New User</h3>
                <p class="action-description">Create student, teacher, or admin accounts</p>
            </a>

            <a href="{{ url_for('results.add_result') }}" class="action-card">
                <div class="action-icon" style="background: var(--success-600);">
                    <i class="bi bi-graph-up"></i>
                </div>
                <h3 class="action-title">Add Result</h3>
                <p class="action-description">Record new student examination results</p>
            </a>

            <a href="{{ url_for('fees.add_fee') }}" class="action-card">
                <div class="action-icon" style="background: var(--warning-600);">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <h3 class="action-title">Add Fee Record</h3>
                <p class="action-description">Create new fee entries for students</p>
            </a>

            <a href="{{ url_for('fees.financial_reports') }}" class="action-card">
                <div class="action-icon" style="background: var(--info-600);">
                    <i class="bi bi-bar-chart-fill"></i>
                </div>
                <h3 class="action-title">View Reports</h3>
                <p class="action-description">Access comprehensive system reports</p>
            </a>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Recent Activities -->
            <div class="content-card">
                <div class="content-header">
                    <i class="bi bi-clock-history" style="color: var(--primary-600);"></i>
                    <h2 class="content-title">Recent Activities</h2>
                </div>

                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-content">
                            <div class="activity-icon">
                                <i class="bi bi-person-plus"></i>
                            </div>
                            <div class="activity-text">
                                <div class="activity-title">New Student Registration</div>
                                <div class="activity-description">Sarah Johnson has been registered as a new student in Computer Science program</div>
                                <div class="activity-time">2 hours ago</div>
                            </div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-content">
                            <div class="activity-icon">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="activity-text">
                                <div class="activity-title">Fee Payment Received</div>
                                <div class="activity-description">Michael Chen completed payment of $2,500 for semester fees</div>
                                <div class="activity-time">4 hours ago</div>
                            </div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-content">
                            <div class="activity-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="activity-text">
                                <div class="activity-title">Exam Results Published</div>
                                <div class="activity-description">Mathematics final exam results have been published for Grade 12</div>
                                <div class="activity-time">6 hours ago</div>
                            </div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-content">
                            <div class="activity-icon">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="activity-text">
                                <div class="activity-title">Teacher Assignment</div>
                                <div class="activity-description">Dr. Emily Rodriguez assigned to Advanced Physics course</div>
                                <div class="activity-time">1 day ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Overview -->
            <div class="content-card">
                <div class="content-header">
                    <i class="bi bi-pie-chart" style="color: var(--primary-600);"></i>
                    <h2 class="content-title">System Overview</h2>
                </div>

                <div class="overview-list">
                    <div class="overview-item">
                        <div>
                            <div class="overview-label">Active Students</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%; background: linear-gradient(90deg, var(--success-500), var(--success-600));"></div>
                                </div>
                            </div>
                        </div>
                        <div class="overview-value">189</div>
                    </div>

                    <div class="overview-item">
                        <div>
                            <div class="overview-label">Teaching Staff</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 72%; background: linear-gradient(90deg, var(--info-500), var(--info-600));"></div>
                                </div>
                            </div>
                        </div>
                        <div class="overview-value">24</div>
                    </div>

                    <div class="overview-item">
                        <div>
                            <div class="overview-label">Fee Collection Rate</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 84%; background: linear-gradient(90deg, var(--warning-500), var(--warning-600));"></div>
                                </div>
                            </div>
                        </div>
                        <div class="overview-value">84%</div>
                    </div>

                    <div class="overview-item">
                        <div>
                            <div class="overview-label">System Performance</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 98%; background: linear-gradient(90deg, var(--success-500), var(--success-600));"></div>
                                </div>
                            </div>
                        </div>
                        <div class="overview-value">98%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add animation to stats cards
document.addEventListener('DOMContentLoaded', function() {
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
    
    // Animate progress bars
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    }, 1000);
});
</script>
{% endblock %}
