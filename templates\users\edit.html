{% extends "layout.html" %}

{% block title %}Edit User - EduManage{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background-color: #f8fafc; min-height: 100vh;">
    <!-- Edit User Header -->
    <div class="bg-primary text-white rounded-3 p-4 mb-4 shadow-sm">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="h2 mb-2">
                    <i class="bi bi-pencil-square me-2"></i>
                    Edit User Account
                </h1>
                <p class="mb-0 opacity-75">Update user account information and manage role-specific details for the education management system.</p>
            </div>
            <div class="col-lg-4 mt-3 mt-lg-0 text-lg-end">
                <a href="{{ url_for('users.users_list') }}" class="btn btn-light btn-lg shadow-sm">
                    <i class="bi bi-arrow-left me-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <!-- User Information Form -->
    <div class="bg-white rounded-3 shadow-sm">
        <div class="bg-light rounded-top-3 p-4 border-bottom">
            <div class="d-flex align-items-center">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                     style="width: 50px; height: 50px;">
                    <i class="bi bi-person-badge fs-4"></i>
                </div>
                <div>
                    <h2 class="mb-0 text-dark">User Information</h2>
                    <p class="text-muted mb-0">Update account details and personal information</p>
                </div>
            </div>
        </div>

        <div class="p-4">
            <form method="POST" class="needs-validation" novalidate>
                <!-- Account Information Section -->
                <div class="row g-4 mb-5">
                    <div class="col-12">
                        <h5 class="text-primary mb-3">
                            <i class="bi bi-shield-check me-2"></i>Account Information
                        </h5>
                    </div>

                    <div class="col-md-6">
                        <label for="username" class="form-label fw-semibold text-dark mb-2">
                            <i class="bi bi-person text-primary me-2"></i>Username
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">👤</span>
                            <input type="text" class="form-control bg-light" id="username" name="username"
                                   value="{{ user.username }}" readonly>
                        </div>
                        <small class="text-muted">Username cannot be changed after account creation</small>
                    </div>

                    <div class="col-md-6">
                        <label for="role" class="form-label fw-semibold text-dark mb-2">
                            <i class="bi bi-shield text-primary me-2"></i>User Role
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">🛡️</span>
                            <input type="text" class="form-control bg-light" id="role" name="role"
                                   value="{{ user.role.title() }}" readonly>
                        </div>
                        <small class="text-muted">Role determines system access permissions</small>
                    </div>
                </div>

                <!-- Personal Information Section -->
                <div class="row g-4 mb-5">
                    <div class="col-12">
                        <h5 class="text-primary mb-3">
                            <i class="bi bi-person-badge me-2"></i>Personal Information
                        </h5>
                    </div>

                    <div class="col-12">
                        <label for="full_name" class="form-label fw-semibold text-dark mb-2">
                            <i class="bi bi-person-fill text-primary me-2"></i>Full Name *
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-primary text-white">👨‍💼</span>
                            <input type="text" class="form-control" id="full_name" name="full_name"
                                   value="{{ user.full_name }}" placeholder="Enter complete full name" required>
                        </div>
                        <div class="invalid-feedback">
                            <i class="bi bi-exclamation-circle me-1"></i>Full name is required and must be at least 2 characters.
                        </div>
                    </div>
                </div>
                    
                <!-- Contact Information Section -->
                <div class="row g-4 mb-5">
                    <div class="col-12">
                        <h5 class="text-primary mb-3">
                            <i class="bi bi-telephone me-2"></i>Contact Information
                        </h5>
                    </div>

                    <div class="col-md-6">
                        <label for="email" class="form-label fw-semibold text-dark mb-2">
                            <i class="bi bi-envelope text-primary me-2"></i>Email Address *
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-primary text-white">📧</span>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="{{ user.email }}" placeholder="Enter valid email address" required>
                        </div>
                        <div class="invalid-feedback">
                            <i class="bi bi-exclamation-circle me-1"></i>Please enter a valid email address.
                        </div>
                        <small class="text-muted">Used for system notifications and password recovery</small>
                    </div>

                    <div class="col-md-6">
                        <label for="phone" class="form-label fw-semibold text-dark mb-2">
                            <i class="bi bi-telephone text-primary me-2"></i>Phone Number
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light">📱</span>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="{{ user.phone or '' }}" placeholder="Enter phone number">
                        </div>
                        <small class="text-muted">Optional contact number for emergencies</small>
                    </div>

                    <div class="col-12">
                        <label for="address" class="form-label fw-semibold text-dark mb-2">
                            <i class="bi bi-geo-alt text-primary me-2"></i>Address
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">🏠</span>
                            <textarea class="form-control" id="address" name="address" rows="3"
                                      placeholder="Enter complete address including city and postal code">{{ user.address or '' }}</textarea>
                        </div>
                        <small class="text-muted">Complete residential or mailing address</small>
                    </div>
                </div>
                    
                <!-- Student-specific fields -->
                {% if user.role == 'student' and student %}
                <div id="student-fields" class="mb-5">
                    <div class="row g-4">
                        <div class="col-12">
                            <h5 class="text-success mb-3">
                                <i class="bi bi-mortarboard me-2"></i>Student Academic Information
                            </h5>
                            <div class="alert alert-success border-0 shadow-sm">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Student Account:</strong> Additional academic and guardian details for student management.
                            </div>
                        </div>

                        <div class="col-md-4">
                            <label for="student_id" class="form-label fw-semibold text-dark mb-2">
                                <i class="bi bi-card-text text-success me-2"></i>Student ID *
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-success text-white">🎓</span>
                                <input type="text" class="form-control" id="student_id" name="student_id"
                                       value="{{ student.student_id }}" placeholder="e.g., STU001" required>
                            </div>
                            <div class="invalid-feedback">
                                <i class="bi bi-exclamation-circle me-1"></i>Student ID is required and must be unique.
                            </div>
                        </div>

                        <div class="col-md-4">
                            <label for="class_name" class="form-label fw-semibold text-dark mb-2">
                                <i class="bi bi-book text-success me-2"></i>Class/Grade *
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-success text-white">📚</span>
                                <input type="text" class="form-control" id="class_name" name="class_name"
                                       value="{{ student.class_name }}" placeholder="e.g., Grade 10" required>
                            </div>
                            <div class="invalid-feedback">
                                <i class="bi bi-exclamation-circle me-1"></i>Class/Grade is required.
                            </div>
                        </div>

                        <div class="col-md-4">
                            <label for="section" class="form-label fw-semibold text-dark mb-2">
                                <i class="bi bi-collection text-success me-2"></i>Section
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-light">📝</span>
                                <input type="text" class="form-control" id="section" name="section"
                                       value="{{ student.section or '' }}" placeholder="e.g., A">
                            </div>
                            <small class="text-muted">Optional class section identifier</small>
                        </div>

                        <div class="col-md-6">
                            <label for="parent_name" class="form-label fw-semibold text-dark mb-2">
                                <i class="bi bi-person-hearts text-success me-2"></i>Parent/Guardian Name
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-light">👨‍👩‍👧‍👦</span>
                                <input type="text" class="form-control" id="parent_name" name="parent_name"
                                       value="{{ student.parent_name or '' }}" placeholder="Enter parent/guardian full name">
                            </div>
                            <small class="text-muted">Primary contact person for student</small>
                        </div>

                        <div class="col-md-6">
                            <label for="parent_phone" class="form-label fw-semibold text-dark mb-2">
                                <i class="bi bi-telephone-plus text-success me-2"></i>Parent/Guardian Phone
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-light">📞</span>
                                <input type="tel" class="form-control" id="parent_phone" name="parent_phone"
                                       value="{{ student.parent_phone or '' }}" placeholder="Enter parent/guardian phone number">
                            </div>
                            <small class="text-muted">Emergency contact for student</small>
                        </div>

                        <div class="col-12">
                            <label for="status" class="form-label fw-semibold text-dark mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>Student Status
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-light">📊</span>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" {{ 'selected' if student.status == 'active' }}>✅ Active - Currently enrolled</option>
                                    <option value="inactive" {{ 'selected' if student.status == 'inactive' }}>⏸️ Inactive - Temporarily suspended</option>
                                    <option value="graduated" {{ 'selected' if student.status == 'graduated' }}>🎓 Graduated - Completed studies</option>
                                </select>
                            </div>
                            <small class="text-muted">Current enrollment status of the student</small>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Account Metadata Section -->
                <div class="row g-4 mb-5">
                    <div class="col-12">
                        <h5 class="text-secondary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Account Information
                        </h5>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold text-muted mb-2">Account Created</label>
                        <div class="p-3 bg-light rounded border">
                            <i class="bi bi-calendar-plus text-success me-2"></i>
                            <span class="fw-semibold">{{ user.created_at|datetime if user.created_at else 'Not available' }}</span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold text-muted mb-2">Last Updated</label>
                        <div class="p-3 bg-light rounded border">
                            <i class="bi bi-clock-history text-info me-2"></i>
                            <span class="fw-semibold">{{ user.updated_at|datetime if user.updated_at else 'Never updated' }}</span>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="bg-light rounded p-4 d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        <i class="bi bi-info-circle me-2"></i>
                        <small>All changes will be saved immediately upon submission</small>
                    </div>
                    <div class="d-flex gap-3">
                        <a href="{{ url_for('users.users_list') }}" class="btn btn-outline-secondary btn-lg px-4">
                            <i class="bi bi-x-circle me-2"></i>Cancel Changes
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg px-4 shadow-sm">
                            <i class="bi bi-check-circle me-2"></i>Update User Account
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced form styling */
    .form-control:focus, .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    }

    .input-group-text {
        font-weight: 600;
        border: 2px solid #dee2e6;
    }

    .form-control, .form-select {
        border: 2px solid #dee2e6;
        transition: all 0.15s ease-in-out;
    }

    .form-control:hover, .form-select:hover {
        border-color: #adb5bd;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .alert {
        border: none;
        font-weight: 500;
    }

    h5 {
        font-weight: 700;
        letter-spacing: -0.025em;
    }

    .bg-light {
        background-color: #f8f9fa !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Form is handled by the global script.js - no additional validation needed
console.log('Users edit form loaded - using global form validation');
</script>
{% endblock %}
