<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Result - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-warning me-2 px-3 py-2">Edit Result</span>
                            <span class="d-none d-sm-inline">{{ result.subject }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/results/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Results
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="bi bi-pencil-square me-3"></i>
                            Edit Student Result
                        </h1>
                        <p class="page-subtitle">Update examination result information and performance metrics.</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-pencil-square me-2"></i>
                                Update Result Information
                            </h5>
                            <small class="opacity-75">Modify examination details and scores</small>
                        </div>
                    <div class="card-body p-5">
                        
                        <!-- Current Result Info -->
                        <div class="info-card">
                            <h5 class="text-warning mb-3">
                                <i class="bi bi-info-circle me-2"></i>Current Result Information
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Student:</strong> {{ result.student_name or 'N/A' }}<br>
                                    <strong>Subject:</strong> {{ result.subject }}<br>
                                    <strong>Exam Type:</strong> {{ result.exam_type }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Current Score:</strong> {{ result.marks_obtained }}/{{ result.total_marks }}<br>
                                    <strong>Grade:</strong> {{ result.grade }}<br>
                                    <strong>Percentage:</strong> {{ "%.1f"|format(result.percentage) }}%
                                </div>
                            </div>
                        </div>

                        <!-- PURE HTML FORM - NO JAVASCRIPT -->
                        <form method="POST" action="/results/edit/{{ result.id }}">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">
                                        <i class="bi bi-book me-1"></i>Subject *
                                    </label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="{{ result.subject }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="exam_type" class="form-label">
                                        <i class="bi bi-clipboard-check me-1"></i>Exam Type *
                                    </label>
                                    <select class="form-select" id="exam_type" name="exam_type" required>
                                        <option value="Midterm" {{ 'selected' if result.exam_type == 'Midterm' }}>📝 Midterm</option>
                                        <option value="Final" {{ 'selected' if result.exam_type == 'Final' }}>🎓 Final</option>
                                        <option value="Quiz" {{ 'selected' if result.exam_type == 'Quiz' }}>❓ Quiz</option>
                                        <option value="Assignment" {{ 'selected' if result.exam_type == 'Assignment' }}>📋 Assignment</option>
                                        <option value="Project" {{ 'selected' if result.exam_type == 'Project' }}>🔬 Project</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="marks_obtained" class="form-label">
                                        <i class="bi bi-award me-1"></i>Marks Obtained *
                                    </label>
                                    <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" 
                                           value="{{ result.marks_obtained }}" required min="0" step="0.01">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="total_marks" class="form-label">
                                        <i class="bi bi-trophy me-1"></i>Total Marks *
                                    </label>
                                    <input type="number" class="form-control" id="total_marks" name="total_marks" 
                                           value="{{ result.total_marks }}" required min="1" step="0.01">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="exam_date" class="form-label">
                                    <i class="bi bi-calendar me-1"></i>Exam Date *
                                </label>
                                <input type="date" class="form-control" id="exam_date" name="exam_date" 
                                       value="{{ result.exam_date.strftime('%Y-%m-%d') if result.exam_date else '' }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>Remarks
                                </label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Optional comments about the result">{{ result.remarks or '' }}</textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-check-circle me-2"></i>Update Result
                                </button>
                                <a href="/results/" class="btn btn-outline-secondary btn-lg ms-3">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 STANDALONE: Results edit page loaded - pure HTML form');
        
        // Simple marks validation
        document.getElementById('marks_obtained').addEventListener('input', function() {
            const marksObtained = parseFloat(this.value) || 0;
            const totalMarks = parseFloat(document.getElementById('total_marks').value) || 0;
            
            if (totalMarks > 0 && marksObtained > totalMarks) {
                this.setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                this.setCustomValidity('');
            }
        });

        document.getElementById('total_marks').addEventListener('input', function() {
            const marksObtained = parseFloat(document.getElementById('marks_obtained').value) || 0;
            const totalMarks = parseFloat(this.value) || 0;
            
            if (totalMarks > 0 && marksObtained > totalMarks) {
                document.getElementById('marks_obtained').setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                document.getElementById('marks_obtained').setCustomValidity('');
            }
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 STANDALONE: Results edit form submitting to:', this.action);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Updating Result...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ STANDALONE: Results edit form ready for submission');
    </script>
</body>
</html>
