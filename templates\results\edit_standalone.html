<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Result - EduManage</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(45deg, #fd7e14, #e55353);
        }
        .form-control:focus, .form-select:focus {
            border-color: #fd7e14;
            box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #fd7e14, #e55353);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #e55353, #dc3545);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .info-card {
            background: #fff3cd;
            border-left: 4px solid #fd7e14;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="bi bi-pencil-square me-3"></i>
                            Edit Student Result
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">Update examination result information</p>
                    </div>
                    <div class="card-body p-5">
                        
                        <!-- Current Result Info -->
                        <div class="info-card">
                            <h5 class="text-warning mb-3">
                                <i class="bi bi-info-circle me-2"></i>Current Result Information
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Student:</strong> {{ result.student_name or 'N/A' }}<br>
                                    <strong>Subject:</strong> {{ result.subject }}<br>
                                    <strong>Exam Type:</strong> {{ result.exam_type }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Current Score:</strong> {{ result.marks_obtained }}/{{ result.total_marks }}<br>
                                    <strong>Grade:</strong> {{ result.grade }}<br>
                                    <strong>Percentage:</strong> {{ "%.1f"|format(result.percentage) }}%
                                </div>
                            </div>
                        </div>

                        <!-- PURE HTML FORM - NO JAVASCRIPT -->
                        <form method="POST" action="/results/edit/{{ result.id }}">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">
                                        <i class="bi bi-book me-1"></i>Subject *
                                    </label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="{{ result.subject }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="exam_type" class="form-label">
                                        <i class="bi bi-clipboard-check me-1"></i>Exam Type *
                                    </label>
                                    <select class="form-select" id="exam_type" name="exam_type" required>
                                        <option value="Midterm" {{ 'selected' if result.exam_type == 'Midterm' }}>📝 Midterm</option>
                                        <option value="Final" {{ 'selected' if result.exam_type == 'Final' }}>🎓 Final</option>
                                        <option value="Quiz" {{ 'selected' if result.exam_type == 'Quiz' }}>❓ Quiz</option>
                                        <option value="Assignment" {{ 'selected' if result.exam_type == 'Assignment' }}>📋 Assignment</option>
                                        <option value="Project" {{ 'selected' if result.exam_type == 'Project' }}>🔬 Project</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="marks_obtained" class="form-label">
                                        <i class="bi bi-award me-1"></i>Marks Obtained *
                                    </label>
                                    <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" 
                                           value="{{ result.marks_obtained }}" required min="0" step="0.01">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="total_marks" class="form-label">
                                        <i class="bi bi-trophy me-1"></i>Total Marks *
                                    </label>
                                    <input type="number" class="form-control" id="total_marks" name="total_marks" 
                                           value="{{ result.total_marks }}" required min="1" step="0.01">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="exam_date" class="form-label">
                                    <i class="bi bi-calendar me-1"></i>Exam Date *
                                </label>
                                <input type="date" class="form-control" id="exam_date" name="exam_date" 
                                       value="{{ result.exam_date.strftime('%Y-%m-%d') if result.exam_date else '' }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>Remarks
                                </label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Optional comments about the result">{{ result.remarks or '' }}</textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-check-circle me-2"></i>Update Result
                                </button>
                                <a href="/results/" class="btn btn-outline-secondary btn-lg ms-3">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 STANDALONE: Results edit page loaded - pure HTML form');
        
        // Simple marks validation
        document.getElementById('marks_obtained').addEventListener('input', function() {
            const marksObtained = parseFloat(this.value) || 0;
            const totalMarks = parseFloat(document.getElementById('total_marks').value) || 0;
            
            if (totalMarks > 0 && marksObtained > totalMarks) {
                this.setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                this.setCustomValidity('');
            }
        });

        document.getElementById('total_marks').addEventListener('input', function() {
            const marksObtained = parseFloat(document.getElementById('marks_obtained').value) || 0;
            const totalMarks = parseFloat(this.value) || 0;
            
            if (totalMarks > 0 && marksObtained > totalMarks) {
                document.getElementById('marks_obtained').setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                document.getElementById('marks_obtained').setCustomValidity('');
            }
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 STANDALONE: Results edit form submitting to:', this.action);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Updating Result...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ STANDALONE: Results edit form ready for submission');
    </script>
</body>
</html>
