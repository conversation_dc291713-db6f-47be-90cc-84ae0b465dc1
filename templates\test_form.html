<!DOCTYPE html>
<html>
<head>
    <title>Test Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Form Submission</h2>
        
        <form method="POST" action="/test-submit">
            <div class="mb-3">
                <label for="test_field" class="form-label">Test Field</label>
                <input type="text" class="form-control" id="test_field" name="test_field" value="test data">
            </div>
            <button type="submit" class="btn btn-primary">Submit Test</button>
        </form>
        
        <hr>
        
        <h3>Results Form Test</h3>
        <form method="POST" action="/results/add">
            <div class="mb-3">
                <label for="student_id" class="form-label">Student ID</label>
                <input type="text" class="form-control" id="student_id" name="student_id" value="1">
            </div>
            <div class="mb-3">
                <label for="subject" class="form-label">Subject</label>
                <input type="text" class="form-control" id="subject" name="subject" value="Math">
            </div>
            <div class="mb-3">
                <label for="exam_type" class="form-label">Exam Type</label>
                <input type="text" class="form-control" id="exam_type" name="exam_type" value="Final">
            </div>
            <div class="mb-3">
                <label for="marks_obtained" class="form-label">Marks Obtained</label>
                <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" value="85">
            </div>
            <div class="mb-3">
                <label for="total_marks" class="form-label">Total Marks</label>
                <input type="number" class="form-control" id="total_marks" name="total_marks" value="100">
            </div>
            <div class="mb-3">
                <label for="exam_date" class="form-label">Exam Date</label>
                <input type="date" class="form-control" id="exam_date" name="exam_date" value="2024-01-15">
            </div>
            <button type="submit" class="btn btn-success">Submit Result</button>
        </form>
    </div>
</body>
</html>