<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Forms - EduManage</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>

<!-- Flash Messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="container mt-3">
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                    <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-clipboard-check me-2"></i>Test Form Submission</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/test-submit">
                        <div class="mb-3">
                            <label for="test_field" class="form-label">Test Field</label>
                            <input type="text" class="form-control" id="test_field" name="test_field"
                                   value="test data" required>
                            <div class="invalid-feedback">
                                Please provide a test value.
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Submit Test
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center mt-4">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="bi bi-graph-up me-2"></i>Quick Result Test</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/results/add">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student_id" class="form-label">Student ID</label>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="1">Student 1</option>
                                    <option value="2">Student 2</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject"
                                       value="Test Math" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="exam_type" class="form-label">Exam Type</label>
                                <input type="text" class="form-control" id="exam_type" name="exam_type"
                                       value="Test Exam" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="marks_obtained" class="form-label">Marks Obtained</label>
                                <input type="number" class="form-control" id="marks_obtained" name="marks_obtained"
                                       value="85" required min="0">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="total_marks" class="form-label">Total Marks</label>
                                <input type="number" class="form-control" id="total_marks" name="total_marks"
                                       value="100" required min="1">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="exam_date" class="form-label">Exam Date</label>
                            <input type="date" class="form-control" id="exam_date" name="exam_date"
                                   value="2024-01-15" required>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-plus-circle me-2"></i>Submit Result
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
console.log('Simple test page loaded - no form validation interference');

// Simple form submission logging
document.addEventListener('submit', function(e) {
    console.log('Form submitted:', e.target.action);
    console.log('Form method:', e.target.method);
    console.log('Form data:', new FormData(e.target));

    // Show loading on submit button
    const submitBtn = e.target.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Submitting...';
        submitBtn.disabled = true;
    }
});
</script>

</body>
</html>