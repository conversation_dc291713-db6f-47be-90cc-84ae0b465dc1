{% extends "layout.html" %}

{% block title %}Test Forms - EduManage{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-clipboard-check me-2"></i>Test Form Submission</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/test-submit" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="test_field" class="form-label">Test Field</label>
                            <input type="text" class="form-control" id="test_field" name="test_field"
                                   value="test data" required>
                            <div class="invalid-feedback">
                                Please provide a test value.
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Submit Test
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center mt-4">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="bi bi-graph-up me-2"></i>Quick Result Test</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/results/add" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student_id" class="form-label">Student ID</label>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="1">Student 1</option>
                                    <option value="2">Student 2</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject"
                                       value="Test Math" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="exam_type" class="form-label">Exam Type</label>
                                <input type="text" class="form-control" id="exam_type" name="exam_type"
                                       value="Test Exam" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="marks_obtained" class="form-label">Marks Obtained</label>
                                <input type="number" class="form-control" id="marks_obtained" name="marks_obtained"
                                       value="85" required min="0">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="total_marks" class="form-label">Total Marks</label>
                                <input type="number" class="form-control" id="total_marks" name="total_marks"
                                       value="100" required min="1">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="exam_date" class="form-label">Exam Date</label>
                            <input type="date" class="form-control" id="exam_date" name="exam_date"
                                   value="2024-01-15" required>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-plus-circle me-2"></i>Submit Result
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}