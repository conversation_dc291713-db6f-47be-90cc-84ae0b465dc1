{% extends "layout.html" %}

{% block title %}Login - EduManage Student Management System{% endblock %}

{% block extra_css %}
<style>
    /* Login Page Specific Styles */
    .login-page {
        background: var(--gray-50);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--space-8) var(--space-4);
    }

    .login-container {
        width: 100%;
        max-width: 1000px;
    }

    .login-card {
        background: white;
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        border: 1px solid var(--gray-200);
    }

    .login-form-section {
        padding: var(--space-12) var(--space-8);
    }

    .login-info-section {
        background: var(--primary-600);
        color: white;
        padding: var(--space-12) var(--space-8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }

    .login-info-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
        opacity: 0.9;
    }

    .login-info-content {
        position: relative;
        z-index: 1;
    }

    .login-header {
        text-align: center;
        margin-bottom: var(--space-10);
    }

    .login-brand {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }

    .brand-logo-large {
        width: 72px;
        height: 72px;
        background: var(--primary-600);
        color: white;
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-3xl);
        box-shadow: var(--shadow-lg);
        flex-shrink: 0;
    }

    .login-brand-text h1 {
        margin: 0;
        font-size: var(--text-3xl);
        font-weight: 700;
        color: var(--primary-600);
        line-height: 1.1;
    }

    .login-brand-text small {
        font-size: var(--text-sm);
        color: var(--gray-500);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: var(--space-1);
        display: block;
    }

    .login-title {
        font-size: var(--text-2xl);
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--space-2);
    }

    .login-subtitle {
        color: var(--gray-600);
        margin-bottom: 0;
        font-size: var(--text-base);
        line-height: 1.5;
    }

    .form-group {
        margin-bottom: var(--space-6);
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: var(--space-2);
        font-size: var(--text-sm);
    }

    .form-label i {
        color: var(--primary-600);
        margin-right: var(--space-2);
    }

    .form-control {
        width: 100%;
        padding: var(--space-4) var(--space-4);
        border: 2px solid var(--gray-300);
        border-radius: var(--radius-lg);
        font-size: var(--text-base);
        font-weight: 500;
        color: var(--gray-900);
        background: white;
        transition: all var(--transition-base);
        line-height: 1.5;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        background: white;
    }

    .form-control:hover {
        border-color: var(--gray-400);
    }

    .form-control::placeholder {
        color: var(--gray-500);
        opacity: 1;
    }

    .input-group {
        position: relative;
    }

    .input-group .form-control {
        padding-right: var(--space-12);
    }

    .input-group .btn {
        position: absolute;
        right: var(--space-1);
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--gray-500);
        padding: var(--space-2);
        border-radius: var(--radius-md);
    }

    .input-group .btn:hover {
        color: var(--primary-600);
        background: var(--gray-100);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-bottom: var(--space-6);
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        border: 2px solid var(--gray-300);
        border-radius: var(--radius-sm);
        background: white;
        transition: all var(--transition-base);
    }

    .form-check-input:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-check-input:checked {
        background-color: var(--primary-600);
        border-color: var(--primary-600);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
    }

    .form-check-input:hover {
        border-color: var(--gray-400);
    }

    .form-check-label {
        color: var(--gray-600);
        font-size: var(--text-sm);
        cursor: pointer;
    }

    .btn-login {
        width: 100%;
        background: var(--primary-600);
        color: white;
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--space-4) var(--space-6);
        font-weight: 600;
        font-size: var(--text-base);
        transition: all var(--transition-base);
        margin-bottom: var(--space-6);
    }

    .btn-login:hover {
        background: var(--primary-700);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }

    .btn-login:active {
        transform: translateY(0);
    }

    .forgot-password {
        text-align: center;
    }

    .forgot-password a {
        color: var(--primary-600);
        text-decoration: none;
        font-weight: 500;
        font-size: var(--text-sm);
    }

    .forgot-password a:hover {
        color: var(--primary-700);
        text-decoration: underline;
    }

    .feature-grid {
        display: grid;
        gap: var(--space-6);
        margin-top: var(--space-8);
    }

    .feature-item {
        text-align: center;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        font-size: var(--text-2xl);
    }

    .feature-title {
        font-size: var(--text-lg);
        font-weight: 600;
        margin-bottom: var(--space-2);
    }

    .feature-description {
        font-size: var(--text-sm);
        opacity: 0.9;
        line-height: 1.5;
    }

    .demo-credentials {
        position: fixed;
        bottom: var(--space-6);
        left: var(--space-6);
        background: white;
        border-radius: var(--radius-xl);
        padding: var(--space-5);
        box-shadow: var(--shadow-xl);
        border: 1px solid var(--gray-200);
        max-width: 280px;
        z-index: var(--z-fixed);
    }

    .demo-title {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-bottom: var(--space-4);
        font-weight: 600;
        color: var(--gray-900);
    }

    .demo-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-bottom: var(--space-3);
        font-size: var(--text-sm);
    }

    .demo-item:last-child {
        margin-bottom: 0;
    }

    .demo-badge {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-md);
        font-size: var(--text-xs);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .demo-badge.admin {
        background: var(--danger-100);
        color: var(--danger-700);
    }

    .demo-badge.teacher {
        background: var(--warning-100);
        color: var(--warning-700);
    }

    .demo-badge.student {
        background: var(--success-100);
        color: var(--success-700);
    }

    .demo-credentials code {
        background: var(--gray-100);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-size: var(--text-xs);
        color: var(--gray-700);
    }

    /* Responsive Design */
    @media (max-width: 991.98px) {
        .login-brand {
            flex-direction: column;
            gap: var(--space-3);
        }

        .brand-logo-large {
            width: 64px;
            height: 64px;
            font-size: var(--text-2xl);
        }

        .login-brand-text h1 {
            font-size: var(--text-2xl);
        }

        .login-brand-text small {
            font-size: var(--text-xs);
        }
    }

    @media (max-width: 767.98px) {
        .login-page {
            padding: var(--space-4);
        }

        .login-form-section,
        .login-info-section {
            padding: var(--space-8) var(--space-6);
        }

        .login-brand {
            margin-bottom: var(--space-8);
        }

        .brand-logo-large {
            width: 56px;
            height: 56px;
            font-size: var(--text-xl);
        }

        .login-brand-text h1 {
            font-size: var(--text-xl);
        }

        .login-title {
            font-size: var(--text-xl);
        }

        .demo-credentials {
            position: relative;
            bottom: auto;
            left: auto;
            margin-top: var(--space-6);
            max-width: 100%;
        }
    }

    @media (max-width: 575.98px) {
        .login-page {
            padding: var(--space-3);
        }

        .login-form-section,
        .login-info-section {
            padding: var(--space-6) var(--space-4);
        }

        .login-brand {
            gap: var(--space-2);
            margin-bottom: var(--space-6);
        }

        .brand-logo-large {
            width: 48px;
            height: 48px;
            font-size: var(--text-lg);
        }

        .login-brand-text h1 {
            font-size: var(--text-lg);
        }

        .login-brand-text small {
            display: none;
        }

        .login-title {
            font-size: var(--text-lg);
        }

        .login-subtitle {
            font-size: var(--text-sm);
        }

        .form-control {
            padding: var(--space-3) var(--space-3);
            font-size: var(--text-sm);
        }

        .btn-login {
            padding: var(--space-3) var(--space-4);
            font-size: var(--text-sm);
        }

        .demo-credentials {
            position: static;
            margin-top: var(--space-4);
            padding: var(--space-4);
            max-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="row g-0">
                <!-- Left side - Login Form -->
                <div class="col-lg-6">
                    <div class="login-form-section">
                        <div class="login-header">
                            <div class="login-brand">
                                <div class="brand-logo-large">
                                    <i class="bi bi-mortarboard-fill"></i>
                                </div>
                                <div class="login-brand-text">
                                    <h1>EduManage</h1>
                                    <small>Student Management System</small>
                                </div>
                            </div>

                            <h2 class="login-title">Welcome Back!</h2>
                            <p class="login-subtitle">Sign in to access your dashboard and manage your educational data</p>
                        </div>

                        <form method="POST" action="/auth/login">
                            <div class="form-group">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person"></i>Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username"
                                       placeholder="Enter your username" value="admin">
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i>Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="Enter your password" value="admin123">
                                    <button type="button" class="btn" id="togglePassword">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me for 30 days
                                </label>
                            </div>

                            <input type="submit" class="btn-login" value="Sign In to Dashboard">

                            <div class="forgot-password">
                                <a href="#">
                                    <i class="bi bi-question-circle me-1"></i>Forgot your password?
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Right side - Information Panel -->
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="login-info-section">
                        <div class="login-info-content text-center">
                            <i class="bi bi-graph-up-arrow" style="font-size: 4rem; margin-bottom: 2rem; opacity: 0.9;"></i>
                            <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">EduManage</h1>
                            <p style="font-size: 1.125rem; margin-bottom: 3rem; opacity: 0.9;">Complete Student Management Solution</p>

                            <div class="feature-grid">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="bi bi-people-fill"></i>
                                    </div>
                                    <h3 class="feature-title">User Management</h3>
                                    <p class="feature-description">Manage students, teachers & administrators with comprehensive role-based access control</p>
                                </div>

                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="bi bi-graph-up"></i>
                                    </div>
                                    <h3 class="feature-title">Academic Results</h3>
                                    <p class="feature-description">Track and analyze student performance with detailed reporting and analytics</p>
                                </div>

                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                    <h3 class="feature-title">Financial Management</h3>
                                    <p class="feature-description">Handle payments and financial records with automated tracking and reporting</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (password.type === 'password') {
        password.type = 'text';
        toggleIcon.classList.remove('bi-eye');
        toggleIcon.classList.add('bi-eye-slash');
    } else {
        password.type = 'password';
        toggleIcon.classList.remove('bi-eye-slash');
        toggleIcon.classList.add('bi-eye');
    }
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Add loading animation to login button
document.querySelector('form').addEventListener('submit', function() {
    const btn = document.querySelector('.btn-login');
    btn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Signing In...';
    btn.disabled = true;
});
</script>
{% endblock %}
