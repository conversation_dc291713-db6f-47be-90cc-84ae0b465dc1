<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Result Confirmation - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-danger me-2 px-3 py-2">Delete Result</span>
                            <span class="d-none d-sm-inline">{{ result.subject }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/results/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Results
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title text-danger">
                            <i class="bi bi-exclamation-triangle me-3"></i>
                            Delete Result Confirmation
                        </h1>
                        <p class="page-subtitle">This action will permanently delete the examination result. This cannot be undone.</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Warning Alert -->
                    <div class="alert alert-warning border-warning" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-3 fs-2"></i>
                            <div>
                                <h5 class="alert-heading mb-2">⚠️ DELETION WARNING</h5>
                                <p class="mb-0">You are about to permanently delete this examination result. This action cannot be undone!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Result Information -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-clipboard-x me-2"></i>
                                Result to be Deleted
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">Result Details</h6>
                                    <p><strong>Student:</strong> {{ result.student_name }}</p>
                                    <p><strong>Student ID:</strong> {{ result.student_id }}</p>
                                    <p><strong>Subject:</strong> {{ result.subject }}</p>
                                    <p><strong>Exam Type:</strong> {{ result.exam_type }}</p>
                                    <p><strong>Exam Date:</strong> {{ result.exam_date.strftime('%Y-%m-%d') if result.exam_date else 'N/A' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">Performance Details</h6>
                                    <p><strong>Marks Obtained:</strong> {{ result.marks_obtained }}</p>
                                    <p><strong>Total Marks:</strong> {{ result.total_marks }}</p>
                                    <p><strong>Percentage:</strong> {{ "%.1f"|format((result.marks_obtained / result.total_marks) * 100) }}%</p>
                                    <p><strong>Grade:</strong> 
                                        <span class="badge bg-{{ 'success' if result.grade in ['A+', 'A'] else 'warning' if result.grade in ['B', 'C'] else 'danger' }}">
                                            {{ result.grade }}
                                        </span>
                                    </p>
                                    <p><strong>Teacher:</strong> {{ result.teacher_name or 'Not specified' }}</p>
                                </div>
                            </div>
                            {% if result.remarks %}
                            <div class="mt-3">
                                <h6 class="text-muted">Remarks:</h6>
                                <p class="text-muted">{{ result.remarks }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Confirmation Form -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 text-danger">
                                <i class="bi bi-shield-exclamation me-2"></i>
                                Confirm Deletion
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6 class="alert-heading">What will be deleted:</h6>
                                <ul class="mb-0">
                                    <li>Examination result for <strong>{{ result.student_name }}</strong></li>
                                    <li>Subject: <strong>{{ result.subject }}</strong> ({{ result.exam_type }})</li>
                                    <li>Score: <strong>{{ result.marks_obtained }}/{{ result.total_marks }}</strong> ({{ result.grade }})</li>
                                    <li>All associated performance data</li>
                                </ul>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <form method="POST" action="/results/delete/{{ result.id }}" style="display: inline;">
                                        <button type="submit" class="btn btn-danger btn-lg w-100" 
                                                onclick="return confirm('Are you sure you want to delete this result?')">
                                            <i class="bi bi-trash me-2"></i>
                                            Yes, Delete Result
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <a href="/results/" class="btn btn-secondary btn-lg w-100">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        Cancel - Keep Result
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('🎯 THEMED: Result delete confirmation page loaded');
        
        // Add confirmation for deletion
        document.querySelector('form').addEventListener('submit', function(e) {
            const confirmed = confirm('⚠️ Are you sure you want to delete this result? This action cannot be undone.');
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Deleting...';
                submitBtn.disabled = true;
            }
        });
    </script>
</body>
</html>
