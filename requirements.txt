# Student Result Management System - Dependencies
# Core Flask framework and extensions
Flask==2.3.3
Werkzeug==2.3.7

# Database (SQLite is built into Python, no additional package needed)
# But we include these for potential future database migrations
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5

# Security and password hashing
bcrypt==4.0.1

# Date and time handling
python-dateutil==2.8.2

# Environment variables (optional, for production)
python-dotenv==1.0.0

# Development and testing tools (optional)
pytest==7.4.2
pytest-flask==1.2.0

# Production server (optional, for deployment)
gunicorn==21.2.0

# Additional utilities
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
