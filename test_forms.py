#!/usr/bin/env python3
"""
Test script to verify form submissions are working
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:5000"
session = requests.Session()

def test_login():
    """Test login functionality"""
    print("🔐 Testing login...")
    
    # Get login page first
    response = session.get(f"{BASE_URL}/auth/login")
    if response.status_code != 200:
        print(f"❌ Failed to get login page: {response.status_code}")
        return False
    
    # Login with admin credentials
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 302:  # Redirect after successful login
        print("✅ Login successful!")
        return True
    else:
        print(f"❌ Login failed: {response.status_code}")
        return False

def test_add_result_form():
    """Test adding a result"""
    print("📊 Testing add result form...")
    
    # Get the add result page
    response = session.get(f"{BASE_URL}/results/add")
    if response.status_code != 200:
        print(f"❌ Failed to get add result page: {response.status_code}")
        return False
    
    # Submit a test result
    result_data = {
        'student_id': '1',
        'subject': 'Test Subject',
        'exam_type': 'Test Exam',
        'marks_obtained': '85',
        'total_marks': '100',
        'exam_date': '2024-01-15'
    }
    
    response = session.post(f"{BASE_URL}/results/add", data=result_data)
    if response.status_code == 302:  # Redirect after successful submission
        print("✅ Add result form submitted successfully!")
        return True
    else:
        print(f"❌ Add result form failed: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        return False

def test_add_user_form():
    """Test adding a user"""
    print("👤 Testing add user form...")
    
    # Get the add user page
    response = session.get(f"{BASE_URL}/users/add")
    if response.status_code != 200:
        print(f"❌ Failed to get add user page: {response.status_code}")
        return False
    
    # Submit a test user
    user_data = {
        'username': 'testuser123',
        'password': 'testpass123',
        'role': 'student',
        'full_name': 'Test User',
        'email': '<EMAIL>',
        'phone': '1234567890',
        'address': 'Test Address',
        'student_id': 'STU123',
        'class_name': 'Grade 10',
        'section': 'A',
        'parent_name': 'Test Parent',
        'parent_phone': '0987654321'
    }
    
    response = session.post(f"{BASE_URL}/users/add", data=user_data)
    if response.status_code == 302:  # Redirect after successful submission
        print("✅ Add user form submitted successfully!")
        return True
    else:
        print(f"❌ Add user form failed: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        return False

def test_add_fee_form():
    """Test adding a fee"""
    print("💰 Testing add fee form...")
    
    # Get the add fee page
    response = session.get(f"{BASE_URL}/fees/add")
    if response.status_code != 200:
        print(f"❌ Failed to get add fee page: {response.status_code}")
        return False
    
    # Submit a test fee
    fee_data = {
        'student_id': '1',
        'fee_type': 'Tuition',
        'amount': '1000.00',
        'due_date': '2024-02-15',
        'remarks': 'Test fee record'
    }
    
    response = session.post(f"{BASE_URL}/fees/add", data=fee_data)
    if response.status_code == 302:  # Redirect after successful submission
        print("✅ Add fee form submitted successfully!")
        return True
    else:
        print(f"❌ Add fee form failed: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        return False

def main():
    """Run all tests"""
    print("🧪 Starting form submission tests...\n")
    
    tests = [
        test_login,
        test_add_result_form,
        test_add_user_form,
        test_add_fee_form
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}\n")
    
    print(f"📈 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Forms are working correctly.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
