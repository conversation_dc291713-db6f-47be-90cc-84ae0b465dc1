{% extends "layout.html" %}

{% block title %}Change Password - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Change Password Header -->
        <div class="page-header">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title">
                            <i class="bi bi-key"></i>
                            Change Password
                        </h1>
                        <p class="page-subtitle">Update your account password to maintain security and protect your personal information.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions">
                            <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Password Form -->
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-shield-lock"></i>
                            Password Security
                        </h2>
                    </div>

                    <!-- Security Tips -->
                    <div class="alert alert-info mb-5">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>Password Security Tips
                        </h6>
                        <ul class="mb-0 small">
                            <li>Use at least 8 characters</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Add numbers and special characters</li>
                            <li>Avoid common words or personal information</li>
                        </ul>
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="form-floating mb-4">
                            <input type="password" class="form-control" id="current_password" name="current_password"
                                   placeholder="Current Password" required>
                            <label for="current_password">
                                <i class="bi bi-lock me-2"></i>Current Password *
                            </label>
                            <div class="invalid-feedback">
                                Please enter your current password.
                            </div>
                        </div>

                        <div class="form-floating mb-4">
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   placeholder="New Password" required minlength="6">
                            <label for="new_password">
                                <i class="bi bi-key me-2"></i>New Password *
                            </label>
                            <div class="invalid-feedback">
                                New password must be at least 6 characters long.
                            </div>
                        </div>

                        <!-- Password strength indicator -->
                        <div class="mb-4">
                            <div class="progress" style="height: 8px; border-radius: var(--radius-lg);">
                                <div class="progress-bar" id="password_strength" role="progressbar" style="width: 0%; transition: all 0.3s ease;"></div>
                            </div>
                            <small class="text-muted mt-2 d-block" id="password_strength_text">Password strength will be shown here</small>
                        </div>

                        <div class="form-floating mb-4">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   placeholder="Confirm Password" required>
                            <label for="confirm_password">
                                <i class="bi bi-check-circle me-2"></i>Confirm New Password *
                            </label>
                            <div class="invalid-feedback">
                                Please confirm your new password.
                            </div>
                        </div>

                        <!-- Password match indicator -->
                        <div class="mb-4" id="password_match" style="display: none;">
                            <div class="alert alert-success py-3">
                                <i class="bi bi-check-circle me-2"></i>
                                <small>Passwords match perfectly!</small>
                            </div>
                        </div>

                        <div class="mb-4" id="password_mismatch" style="display: none;">
                            <div class="alert alert-danger py-3">
                                <i class="bi bi-x-circle me-2"></i>
                                <small>Passwords do not match!</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Update Password
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Account Security Info -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-clock-history"></i>
                            Account Security
                        </h2>
                    </div>

                    <div class="row g-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center py-3 border-bottom">
                                <div>
                                    <div class="fw-semibold">Last Password Change</div>
                                    <small class="text-muted">Keep your password updated regularly for security</small>
                                </div>
                                <div class="text-end">
                                    <span class="status-badge active">{{ current_user.updated_at|datetime }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center py-3">
                                <div>
                                    <div class="fw-semibold">Account Created</div>
                                    <small class="text-muted">Your account registration date</small>
                                </div>
                                <div class="text-end">
                                    <span class="status-badge pending">{{ current_user.created_at|datetime }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

// Password strength checker
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password_strength');
    const strengthText = document.getElementById('password_strength_text');
    
    let strength = 0;
    let feedback = '';
    
    // Length check
    if (password.length >= 8) strength += 20;
    
    // Lowercase check
    if (/[a-z]/.test(password)) strength += 20;
    
    // Uppercase check
    if (/[A-Z]/.test(password)) strength += 20;
    
    // Number check
    if (/\d/.test(password)) strength += 20;
    
    // Special character check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 20;
    
    // Update progress bar
    strengthBar.style.width = strength + '%';
    
    // Update color and text
    if (strength < 40) {
        strengthBar.className = 'progress-bar bg-danger';
        feedback = 'Weak password';
    } else if (strength < 60) {
        strengthBar.className = 'progress-bar bg-warning';
        feedback = 'Fair password';
    } else if (strength < 80) {
        strengthBar.className = 'progress-bar bg-info';
        feedback = 'Good password';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        feedback = 'Strong password';
    }
    
    strengthText.textContent = feedback;
});

// Password confirmation checker
function checkPasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchDiv = document.getElementById('password_match');
    const mismatchDiv = document.getElementById('password_mismatch');
    
    if (confirmPassword.length > 0) {
        if (newPassword === confirmPassword) {
            matchDiv.style.display = 'block';
            mismatchDiv.style.display = 'none';
        } else {
            matchDiv.style.display = 'none';
            mismatchDiv.style.display = 'block';
        }
    } else {
        matchDiv.style.display = 'none';
        mismatchDiv.style.display = 'none';
    }
}

document.getElementById('new_password').addEventListener('input', checkPasswordMatch);
document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);

// Form validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
