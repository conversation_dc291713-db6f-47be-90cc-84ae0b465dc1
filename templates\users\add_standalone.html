<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New User - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-primary me-2 px-3 py-2">Admin</span>
                            <span class="d-none d-sm-inline">Add New User</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="bi bi-person-plus-fill me-3"></i>
                            Add New User
                        </h1>
                        <p class="page-subtitle">Create a new user account with role-based permissions for the education management system.</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person-circle me-2"></i>
                                User Account Information
                            </h5>
                            <small class="opacity-75">Create a new user account with role-based permissions</small>
                        </div>
                    <div class="card-body p-5">
                        <!-- PURE HTML FORM - NO JAVASCRIPT -->
                        <form method="POST" action="/users/add">
                            
                            <!-- User Account Information -->
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-person-circle me-2"></i>User Account Information
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            <i class="bi bi-person me-1"></i>Username *
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                        <div class="form-text">Will be auto-generated from full name if left empty</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">
                                            <i class="bi bi-shield-check me-1"></i>User Role *
                                        </label>
                                        <select class="form-select" id="role" name="role" required onchange="toggleStudentFields()">
                                            <option value="">Select Role</option>
                                            <option value="admin">👨‍💼 Administrator</option>
                                            <option value="teacher">👨‍🏫 Teacher</option>
                                            <option value="student">👨‍🎓 Student</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            <i class="bi bi-lock me-1"></i>Password *
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <div class="form-text">Minimum 6 characters required</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            <i class="bi bi-person-badge me-1"></i>Full Name *
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="bi bi-envelope me-1"></i>Email Address *
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="bi bi-telephone me-1"></i>Phone Number
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                        <div class="form-text">Optional contact number</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">
                                        <i class="bi bi-geo-alt me-1"></i>Address
                                    </label>
                                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                                </div>
                            </div>

                            <!-- Student Information (Hidden by default) -->
                            <div id="student-fields" class="student-fields" style="display: none;">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-mortarboard me-2"></i>Student Information
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_id" class="form-label">Student ID *</label>
                                        <input type="text" class="form-control" id="student_id" name="student_id">
                                        <div class="form-text">Student ID is required for students</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="class_name" class="form-label">Class *</label>
                                        <input type="text" class="form-control" id="class_name" name="class_name">
                                        <div class="form-text">Class is required for students</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="section" class="form-label">Section</label>
                                        <input type="text" class="form-control" id="section" name="section">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="parent_name" class="form-label">Parent/Guardian Name</label>
                                        <input type="text" class="form-control" id="parent_name" name="parent_name">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="parent_phone" class="form-label">Parent/Guardian Phone</label>
                                    <input type="tel" class="form-control" id="parent_phone" name="parent_phone">
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="bi bi-person-plus me-2"></i>Create User Account
                                </button>
                                <a href="/users/" class="btn btn-outline-secondary btn-lg ms-3 px-5">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 STANDALONE: User add page loaded - pure HTML form');
        
        // Simple function to show/hide student fields
        function toggleStudentFields() {
            const roleSelect = document.getElementById('role');
            const studentFields = document.getElementById('student-fields');
            const studentId = document.getElementById('student_id');
            const className = document.getElementById('class_name');
            
            if (roleSelect.value === 'student') {
                studentFields.style.display = 'block';
                studentId.required = true;
                className.required = true;
            } else {
                studentFields.style.display = 'none';
                studentId.required = false;
                className.required = false;
            }
        }

        // Auto-generate username from full name
        document.getElementById('full_name').addEventListener('blur', function() {
            const usernameField = document.getElementById('username');
            if (!usernameField.value && this.value) {
                const username = this.value.toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '')
                    .substring(0, 15);
                usernameField.value = username;
            }
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 STANDALONE: Form submitting to:', this.action);
            console.log('🚀 STANDALONE: Form method:', this.method);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Creating User...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ STANDALONE: Form ready for submission');
    </script>
</body>
</html>
