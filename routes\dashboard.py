#!/usr/bin/env python3
"""
Dashboard Routes
Handles role-based dashboard views and statistics
"""

from flask import Blueprint, render_template, session, redirect, url_for
from routes.auth import login_required
from models.db import get_db_connection, get_dashboard_stats, get_student_by_user_id

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def dashboard():
    """Main dashboard - role-based content"""
    user_role = session.get('role')
    user_id = session.get('user_id')
    
    if user_role == 'admin':
        return admin_dashboard()
    elif user_role == 'teacher':
        return teacher_dashboard()
    elif user_role == 'student':
        return student_dashboard(user_id)
    else:
        return redirect(url_for('auth.login'))

def admin_dashboard():
    """Admin dashboard with system overview"""
    stats = get_dashboard_stats()
    
    conn = get_db_connection()
    
    # Recent users
    recent_users = conn.execute('''
        SELECT full_name, role, created_at
        FROM users
        ORDER BY created_at DESC
        LIMIT 5
    ''').fetchall()
    
    # Overdue fees
    overdue_fees = conn.execute('''
        SELECT f.fee_type, f.amount, f.due_date, u.full_name as student_name, s.student_id
        FROM financial_records f
        JOIN students s ON f.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE f.status = 'overdue'
        ORDER BY f.due_date
        LIMIT 10
    ''').fetchall()
    
    conn.close()
    
    return render_template('dashboard/admin.html', 
                         stats=stats, 
                         recent_users=recent_users,
                         overdue_fees=overdue_fees)

def teacher_dashboard():
    """Teacher dashboard with teaching overview"""
    teacher_id = session.get('user_id')
    conn = get_db_connection()
    
    # Teacher's statistics
    stats = {}
    stats['total_results'] = conn.execute(
        'SELECT COUNT(*) FROM results WHERE teacher_id = ?', (teacher_id,)
    ).fetchone()[0]
    
    stats['total_students'] = conn.execute('''
        SELECT COUNT(DISTINCT student_id) FROM results WHERE teacher_id = ?
    ''', (teacher_id,)).fetchone()[0]
    
    stats['avg_marks'] = conn.execute('''
        SELECT AVG(marks_obtained * 100.0 / total_marks) FROM results WHERE teacher_id = ?
    ''', (teacher_id,)).fetchone()[0] or 0
    
    # Recent results entered by this teacher
    recent_results = conn.execute('''
        SELECT r.subject, r.exam_type, r.marks_obtained, r.total_marks, 
               u.full_name as student_name, s.student_id, r.exam_date
        FROM results r
        JOIN students s ON r.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE r.teacher_id = ?
        ORDER BY r.created_at DESC
        LIMIT 10
    ''', (teacher_id,)).fetchall()
    
    # Subject-wise performance
    subject_performance = conn.execute('''
        SELECT subject, 
               COUNT(*) as total_exams,
               AVG(marks_obtained * 100.0 / total_marks) as avg_percentage,
               MIN(marks_obtained * 100.0 / total_marks) as min_percentage,
               MAX(marks_obtained * 100.0 / total_marks) as max_percentage
        FROM results 
        WHERE teacher_id = ?
        GROUP BY subject
        ORDER BY subject
    ''', (teacher_id,)).fetchall()
    
    conn.close()
    
    return render_template('dashboard/teacher.html',
                         stats=stats,
                         recent_results=recent_results,
                         subject_performance=subject_performance)

def student_dashboard(user_id):
    """Student dashboard with personal academic overview"""
    # Get student record
    student = get_student_by_user_id(user_id)
    if not student:
        return redirect(url_for('auth.login'))

    student_id = student['id']

    # Student statistics
    stats = {}

    # Create connection for all queries
    conn = get_db_connection()

    try:
        # Academic performance
        academic_stats = conn.execute('''
            SELECT
                COUNT(*) as total_exams,
                AVG(marks_obtained * 100.0 / total_marks) as overall_percentage,
                COUNT(DISTINCT subject) as total_subjects
            FROM results
            WHERE student_id = ?
        ''', (student_id,)).fetchone()

        stats['total_exams'] = academic_stats['total_exams'] or 0
        stats['overall_percentage'] = academic_stats['overall_percentage'] or 0
        stats['total_subjects'] = academic_stats['total_subjects'] or 0

        # Financial status
        financial_stats = conn.execute('''
            SELECT
                COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
                COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
                COALESCE(SUM(CASE WHEN status = 'overdue' THEN amount ELSE 0 END), 0) as overdue_amount
            FROM financial_records
            WHERE student_id = ?
        ''', (student_id,)).fetchone()

        stats['paid_fees'] = financial_stats['paid_amount']
        stats['pending_fees'] = financial_stats['pending_amount']
        stats['overdue_fees'] = financial_stats['overdue_amount']

        # Recent results
        recent_results = conn.execute('''
            SELECT subject, exam_type, marks_obtained, total_marks, grade, exam_date,
                   (marks_obtained * 100.0 / total_marks) as percentage
            FROM results
            WHERE student_id = ?
            ORDER BY exam_date DESC, created_at DESC
            LIMIT 10
        ''', (student_id,)).fetchall()

        # Subject-wise performance
        subject_performance = conn.execute('''
            SELECT subject,
                   COUNT(*) as total_exams,
                   AVG(marks_obtained * 100.0 / total_marks) as avg_percentage,
                   MAX(marks_obtained * 100.0 / total_marks) as best_percentage,
                   MIN(marks_obtained * 100.0 / total_marks) as lowest_percentage
            FROM results
            WHERE student_id = ?
            GROUP BY subject
            ORDER BY avg_percentage DESC
        ''', (student_id,)).fetchall()
    
        # Recent fee records
        recent_fees = conn.execute('''
            SELECT fee_type, amount, due_date, paid_date, status
            FROM financial_records
            WHERE student_id = ?
            ORDER BY due_date DESC
            LIMIT 5
        ''', (student_id,)).fetchall()

        # Grade distribution
        grade_distribution = conn.execute('''
            SELECT grade, COUNT(*) as count
            FROM results
            WHERE student_id = ? AND grade IS NOT NULL
            GROUP BY grade
            ORDER BY
                CASE grade
                    WHEN 'A+' THEN 1
                    WHEN 'A' THEN 2
                    WHEN 'B+' THEN 3
                    WHEN 'B' THEN 4
                    WHEN 'C+' THEN 5
                    WHEN 'C' THEN 6
                    WHEN 'D' THEN 7
                    WHEN 'F' THEN 8
                    ELSE 9
                END
        ''', (student_id,)).fetchall()

    finally:
        conn.close()

    return render_template('dashboard/student.html',
                         student=student,
                         stats=stats,
                         recent_results=recent_results,
                         subject_performance=subject_performance,
                         recent_fees=recent_fees,
                         grade_distribution=grade_distribution)
