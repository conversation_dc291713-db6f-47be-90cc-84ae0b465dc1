{% extends "layout.html" %}

{% block title %}Financial Reports - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Reports Header -->
        <div class="page-header bg-gradient-info">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title text-white mb-2">
                            <i class="bi bi-bar-chart me-3"></i>
                            Financial Reports
                        </h1>
                        <p class="page-subtitle text-white-75 mb-0">Comprehensive financial analytics and performance insights</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions text-end">
                            <button class="btn btn-light btn-lg shadow-sm me-2" onclick="window.print()">
                                <i class="bi bi-printer me-2"></i>Print Report
                            </button>
                            <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-light btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>Back to Fees
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row g-4 mb-5">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card bg-white rounded-3 shadow-sm border-0 h-100 overflow-hidden">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="icon-circle bg-gradient-success text-white d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; border-radius: 50%;">
                                    <i class="bi bi-currency-dollar fs-3"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-4">
                                <div class="fw-bold text-success display-6 mb-1">${{ "%.0f"|format(stats.total_revenue) }}</div>
                                <div class="text-dark fw-semibold mb-2">Total Revenue</div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                                        <i class="bi bi-check-circle me-1"></i>{{ stats.paid_count }} payments
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-success-subtle border-0 py-2">
                        <small class="text-success fw-medium">
                            <i class="bi bi-trending-up me-1"></i>Successfully collected
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card bg-white rounded-3 shadow-sm border-0 h-100 overflow-hidden">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="icon-circle bg-gradient-warning text-white d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; border-radius: 50%;">
                                    <i class="bi bi-clock fs-3"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-4">
                                <div class="fw-bold text-warning display-6 mb-1">${{ "%.0f"|format(stats.pending_amount) }}</div>
                                <div class="text-dark fw-semibold mb-2">Pending Fees</div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-warning-subtle text-warning px-3 py-2 rounded-pill">
                                        <i class="bi bi-hourglass me-1"></i>{{ stats.pending_count }} pending
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-warning-subtle border-0 py-2">
                        <small class="text-warning fw-medium">
                            <i class="bi bi-clock-history me-1"></i>Awaiting payment
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card bg-white rounded-3 shadow-sm border-0 h-100 overflow-hidden">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="icon-circle bg-gradient-danger text-white d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; border-radius: 50%;">
                                    <i class="bi bi-exclamation-triangle fs-3"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-4">
                                <div class="fw-bold text-danger display-6 mb-1">${{ "%.0f"|format(stats.overdue_amount) }}</div>
                                <div class="text-dark fw-semibold mb-2">Overdue Fees</div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-danger-subtle text-danger px-3 py-2 rounded-pill">
                                        <i class="bi bi-exclamation-circle me-1"></i>{{ stats.overdue_count }} overdue
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-danger-subtle border-0 py-2">
                        <small class="text-danger fw-medium">
                            <i class="bi bi-calendar-x me-1"></i>Requires attention
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card bg-white rounded-3 shadow-sm border-0 h-100 overflow-hidden">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="icon-circle bg-gradient-info text-white d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; border-radius: 50%;">
                                    <i class="bi bi-percent fs-3"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-4">
                                <div class="fw-bold text-info display-6 mb-1">
                                    {{ "%.1f"|format((stats.total_revenue / (stats.total_revenue + stats.pending_amount + stats.overdue_amount) * 100) if (stats.total_revenue + stats.pending_amount + stats.overdue_amount) > 0 else 0) }}%
                                </div>
                                <div class="text-dark fw-semibold mb-2">Collection Rate</div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-info-subtle text-info px-3 py-2 rounded-pill">
                                        <i class="bi bi-graph-up me-1"></i>Payment efficiency
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-info-subtle border-0 py-2">
                        <small class="text-info fw-medium">
                            <i class="bi bi-trophy me-1"></i>Performance metric
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fee Type Breakdown and Class-wise Collection -->
        <div class="row g-4 mb-5">
            <!-- Fee Type Breakdown -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm h-100 rounded-3">
                    <div class="card-header bg-light rounded-top-3 border-bottom p-4">
                        <h5 class="card-title mb-0 text-dark">
                            <i class="bi bi-pie-chart text-primary me-2"></i>
                            Fee Type Breakdown
                        </h5>
                        <p class="text-muted mb-0 mt-2 small">Collection performance by fee category</p>
                    </div>
                    <div class="card-body p-0">
                        {% if fee_type_breakdown %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0 align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-bold text-dark border-0 py-3 ps-4">Fee Type</th>
                                            <th class="fw-bold text-dark border-0 py-3">Total</th>
                                            <th class="fw-bold text-dark border-0 py-3">Collected</th>
                                            <th class="fw-bold text-dark border-0 py-3">Pending</th>
                                            <th class="fw-bold text-dark border-0 py-3 pe-4">Rate</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for fee_type in fee_type_breakdown %}
                                        <tr class="border-bottom">
                                            <td class="fw-semibold text-dark ps-4 py-3">
                                                <span class="badge bg-light text-dark border px-3 py-2 rounded-pill">
                                                    {% if fee_type.fee_type == 'Tuition' %}📚{% elif fee_type.fee_type == 'Library' %}📖{% elif fee_type.fee_type == 'Lab' %}🔬{% elif fee_type.fee_type == 'Sports' %}⚽{% elif fee_type.fee_type == 'Transport' %}🚌{% else %}💰{% endif %}
                                                    {{ fee_type.fee_type }}
                                                </span>
                                            </td>
                                            <td class="fw-bold text-dark py-3">${{ "%.0f"|format(fee_type.total_amount) }}</td>
                                            <td class="fw-bold text-success py-3">${{ "%.0f"|format(fee_type.paid_amount) }}</td>
                                            <td class="fw-bold text-warning py-3">${{ "%.0f"|format(fee_type.pending_amount) }}</td>
                                            <td class="pe-4 py-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-3 flex-grow-1" style="height: 8px;">
                                                        <div class="progress-bar bg-gradient-success rounded-pill"
                                                             style="width: {{ (fee_type.paid_amount / fee_type.total_amount * 100) if fee_type.total_amount > 0 else 0 }}%"></div>
                                                    </div>
                                                    <span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill">
                                                        {{ "%.0f"|format((fee_type.paid_amount / fee_type.total_amount * 100) if fee_type.total_amount > 0 else 0) }}%
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-pie-chart text-muted" style="font-size: 3rem; opacity: 0.5;"></i>
                                <h6 class="mt-3 text-dark">No fee data available</h6>
                                <p class="mb-0">Fee breakdown will appear here once data is available</p>
                            </div>
                        {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Class-wise Collection -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart text-primary me-2"></i>
                    Class-wise Collection
                </h5>
            </div>
            <div class="card-body">
                {% if class_breakdown %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Class</th>
                                    <th>Total</th>
                                    <th>Collected</th>
                                    <th>Pending</th>
                                    <th>Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for class_data in class_breakdown %}
                                <tr>
                                    <td class="fw-semibold">{{ class_data.class_name }}</td>
                                    <td>${{ "%.2f"|format(class_data.total_amount) }}</td>
                                    <td class="text-success">${{ "%.2f"|format(class_data.paid_amount) }}</td>
                                    <td class="text-warning">${{ "%.2f"|format(class_data.pending_amount) }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 50px; height: 6px;">
                                                <div class="progress-bar bg-info" 
                                                     style="width: {{ (class_data.paid_amount / class_data.total_amount * 100) if class_data.total_amount > 0 else 0 }}%"></div>
                                            </div>
                                            <small>{{ "%.0f"|format((class_data.paid_amount / class_data.total_amount * 100) if class_data.total_amount > 0 else 0) }}%</small>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-bar-chart fs-1 d-block mb-2"></i>
                        <p>No class data available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Monthly Revenue Trend -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up text-primary me-2"></i>
                    Monthly Revenue Trend (Last 12 Months)
                </h5>
            </div>
            <div class="card-body">
                {% if monthly_revenue %}
                    <div class="row g-3">
                        {% for month_data in monthly_revenue %}
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="fw-bold text-primary">${{ "%.0f"|format(month_data.revenue) }}</div>
                                <div class="text-muted small">{{ month_data.month }}</div>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-primary" 
                                         style="width: {{ (month_data.revenue / monthly_revenue|map(attribute='revenue')|max * 100) if monthly_revenue|map(attribute='revenue')|max > 0 else 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-graph-up fs-1 d-block mb-2"></i>
                        <p>No monthly revenue data available</p>
                        <small>Revenue trends will appear after payments are recorded</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .btn, .navbar, .card-header {
            display: none !important;
        }
        
        .card {
            box-shadow: none !important;
            border: 1px solid #dee2e6 !important;
        }
        
        body {
            background-color: white !important;
        }
    }
    
    .progress {
        border-radius: 10px;
        overflow: hidden;
    }
</style>
{% endblock %}
