#!/usr/bin/env python3
"""
Student Result Management System - Main Application
A complete Flask web application for managing student results, users, and fees
Author: Final Year Project
Technologies: Flask, SQLite, Bootstrap, Jinja2
"""

from flask import Flask, render_template, session, redirect, url_for, flash
from werkzeug.security import generate_password_hash
import os
import sqlite3
from datetime import datetime

# Import blueprints
from routes.auth import auth_bp
from routes.users import users_bp
from routes.results import results_bp
from routes.fees import fees_bp
from routes.dashboard import dashboard_bp

# Import database models
from models.db import init_db, get_db_connection

def create_app():
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
    app.config['DATABASE'] = 'database/database.db'
    
    # Ensure database directory exists
    os.makedirs('database', exist_ok=True)
    
    # Initialize database
    with app.app_context():
        init_db()
        create_sample_data()

    # Register database teardown
    @app.teardown_appcontext
    def close_db(error):
        """Close database connection at the end of request"""
        from models.db import close_db
        close_db(error)
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(users_bp, url_prefix='/users')
    app.register_blueprint(results_bp, url_prefix='/results')
    app.register_blueprint(fees_bp, url_prefix='/fees')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    
    # Main routes
    @app.route('/')
    def index():
        """Home page - redirect to dashboard if logged in, otherwise login"""
        if 'user_id' in session:
            return redirect(url_for('dashboard.dashboard'))
        return redirect(url_for('auth.login'))
    
    @app.route('/profile')
    def profile():
        """User profile page"""
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        
        conn = get_db_connection()
        user = conn.execute(
            'SELECT * FROM users WHERE id = ?', (session['user_id'],)
        ).fetchone()
        conn.close()
        
        return render_template('profile.html', user=user)
    
    # Test routes for debugging form submission
    @app.route('/test-form')
    def test_form():
        """Test form page"""
        return render_template('test_form.html')
    
    @app.route('/test-submit', methods=['POST'])
    def test_submit():
        """Test form submission"""
        from flask import request
        print("=== TEST FORM SUBMISSION ===")
        print(f"Method: {request.method}")
        print(f"Form data: {dict(request.form)}")
        print("=== END TEST ===")
        flash('Test form submitted successfully!', 'success')
        return redirect(url_for('test_forms'))

    @app.route('/test-forms')
    def test_forms():
        """Test form page"""
        return render_template('test_form.html')
    
    # Template filters - Robust datetime handling
    @app.template_filter('date')
    def date_filter(date_value):
        """Format date for templates - handles both strings and datetime objects"""
        if not date_value:
            return ''

        try:
            if isinstance(date_value, str):
                # Try different date formats
                for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f']:
                    try:
                        date_obj = datetime.strptime(date_value, fmt)
                        return date_obj.strftime('%b %d, %Y')
                    except ValueError:
                        continue
                # If no format matches, return the string as is
                return date_value
            else:
                # Assume it's a datetime object
                return date_value.strftime('%b %d, %Y')
        except Exception:
            return str(date_value)

    @app.template_filter('datetime')
    def datetime_filter(datetime_value):
        """Format datetime for templates - handles both strings and datetime objects"""
        if not datetime_value:
            return ''

        try:
            if isinstance(datetime_value, str):
                # Try different datetime formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                    try:
                        datetime_obj = datetime.strptime(datetime_value, fmt)
                        return datetime_obj.strftime('%B %d, %Y at %I:%M %p')
                    except ValueError:
                        continue
                # If no format matches, return the string as is
                return datetime_value
            else:
                # Assume it's a datetime object
                return datetime_value.strftime('%B %d, %Y at %I:%M %p')
        except Exception:
            return str(datetime_value)

    @app.template_filter('time')
    def time_filter(datetime_value):
        """Format time for templates - handles both strings and datetime objects"""
        if not datetime_value:
            return ''

        try:
            if isinstance(datetime_value, str):
                # Try different datetime formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f']:
                    try:
                        datetime_obj = datetime.strptime(datetime_value, fmt)
                        return datetime_obj.strftime('%I:%M %p')
                    except ValueError:
                        continue
                # If no format matches, return the string as is
                return datetime_value
            else:
                # Assume it's a datetime object
                return datetime_value.strftime('%I:%M %p')
        except Exception:
            return str(datetime_value)

    # Context processors
    @app.context_processor
    def inject_user():
        """Inject current user into all templates"""
        if 'user_id' in session:
            try:
                # Create a fresh connection for this context
                conn = sqlite3.connect(app.config['DATABASE'])
                conn.row_factory = sqlite3.Row
                user = conn.execute(
                    'SELECT * FROM users WHERE id = ?', (session['user_id'],)
                ).fetchone()
                conn.close()
                return {'current_user': user}
            except Exception as e:
                print(f"Error getting user: {e}")
                return {'current_user': None}
        return {'current_user': None}
    
    return app

def create_sample_data():
    """Create sample data for testing"""
    conn = get_db_connection()
    
    # Check if data already exists
    existing_users = conn.execute('SELECT COUNT(*) FROM users').fetchone()[0]
    if existing_users > 0:
        conn.close()
        return
    
    try:
        # Create sample users
        users_data = [
            ('admin', generate_password_hash('admin123'), 'admin', 'System Administrator', '<EMAIL>', '555-0001', '123 Admin St'),
            ('teacher1', generate_password_hash('teacher123'), 'teacher', 'John Smith', '<EMAIL>', '555-0002', '456 Teacher Ave'),
            ('teacher2', generate_password_hash('teacher123'), 'teacher', 'Sarah Johnson', '<EMAIL>', '555-0003', '789 Faculty Rd'),
            ('student1', generate_password_hash('student123'), 'student', 'Alice Brown', '<EMAIL>', '555-0004', '321 Student Ln'),
            ('student2', generate_password_hash('student123'), 'student', 'Bob Wilson', '<EMAIL>', '555-0005', '654 Campus Dr'),
            ('student3', generate_password_hash('student123'), 'student', 'Carol Davis', '<EMAIL>', '555-0006', '987 Dorm St')
        ]
        
        for user_data in users_data:
            conn.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email, phone, address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', user_data)
        
        # Create sample students (additional info for student users)
        students_data = [
            (4, 'STU001', 'Grade 10', 'A', '2024-01-15', 'Robert Brown', '555-1001'),
            (5, 'STU002', 'Grade 10', 'B', '2024-01-16', 'Mary Wilson', '555-1002'),
            (6, 'STU003', 'Grade 11', 'A', '2024-01-17', 'James Davis', '555-1003')
        ]
        
        for student_data in students_data:
            conn.execute('''
                INSERT INTO students (user_id, student_id, class_name, section, admission_date, parent_name, parent_phone)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', student_data)
        
        # Create sample results
        results_data = [
            (1, 'Mathematics', 'Mid Term', 85.0, 100.0, 'A', '2024-03-15', 2),
            (1, 'Physics', 'Mid Term', 78.0, 100.0, 'B+', '2024-03-16', 2),
            (1, 'Chemistry', 'Mid Term', 92.0, 100.0, 'A+', '2024-03-17', 3),
            (2, 'Mathematics', 'Mid Term', 76.0, 100.0, 'B', '2024-03-15', 2),
            (2, 'Physics', 'Mid Term', 82.0, 100.0, 'A-', '2024-03-16', 2),
            (3, 'Mathematics', 'Mid Term', 88.0, 100.0, 'A', '2024-03-15', 2),
            (3, 'English', 'Mid Term', 91.0, 100.0, 'A+', '2024-03-18', 3)
        ]
        
        for result_data in results_data:
            conn.execute('''
                INSERT INTO results (student_id, subject, exam_type, marks_obtained, total_marks, grade, exam_date, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', result_data)
        
        # Create sample financial records
        fees_data = [
            (1, 'Tuition Fee', 1500.00, '2024-04-01', '2024-03-25', 'paid', 'Bank Transfer'),
            (1, 'Lab Fee', 200.00, '2024-04-15', None, 'pending', None),
            (2, 'Tuition Fee', 1500.00, '2024-04-01', None, 'overdue', None),
            (2, 'Library Fee', 100.00, '2024-05-01', None, 'pending', None),
            (3, 'Tuition Fee', 1500.00, '2024-04-01', '2024-03-30', 'paid', 'Cash'),
            (3, 'Sports Fee', 150.00, '2024-04-20', None, 'pending', None)
        ]
        
        for fee_data in fees_data:
            conn.execute('''
                INSERT INTO financial_records (student_id, fee_type, amount, due_date, paid_date, status, payment_method)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', fee_data)
        
        conn.commit()
        print("Sample data created successfully!")
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    app = create_app()
    print("🚀 Starting Student Result Management System...")
    print("📊 Access the application at: http://localhost:5000")
    print("🔑 Default login credentials:")
    print("   Admin: admin / admin123")
    print("   Teacher: teacher1 / teacher123")
    print("   Student: student1 / student123")
    app.run(debug=True, host='0.0.0.0', port=5000)
