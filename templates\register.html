{% extends "layout.html" %}

{% block title %}Register - EduManage{% endblock %}

{% block extra_css %}
<style>
    .register-page {
        background: var(--gray-50);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--space-8) var(--space-4);
    }

    .register-container {
        width: 100%;
        max-width: 1200px;
    }

    .register-card {
        background: white;
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        border: 1px solid var(--gray-200);
    }

    .register-form-section {
        padding: var(--space-12) var(--space-8);
    }

    .register-info-section {
        background: var(--success-600);
        color: white;
        padding: var(--space-12) var(--space-8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }

    .register-info-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }

    .register-title {
        font-size: var(--text-4xl);
        font-weight: 700;
        margin-bottom: var(--space-3);
        color: white;
    }

    .register-subtitle {
        font-size: var(--text-lg);
        opacity: 0.9;
        margin-bottom: var(--space-8);
    }

    .form-floating {
        margin-bottom: var(--space-4);
    }

    .form-floating > .form-control {
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-4) var(--space-4);
        font-size: var(--text-base);
        transition: all var(--transition-base);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: var(--success-500);
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        background: white;
    }

    .form-floating > .form-control:hover,
    .form-floating > .form-select:hover {
        border-color: var(--gray-400);
    }

    .form-floating > .form-control::placeholder {
        color: var(--gray-500);
        opacity: 1;
    }

    .form-floating > label {
        color: var(--gray-600);
        font-weight: 500;
        font-size: var(--text-sm);
        transition: all var(--transition-base);
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select:focus ~ label,
    .form-floating > .form-select:not(:placeholder-shown) ~ label {
        color: var(--success-600);
    }

    .btn-register {
        background: linear-gradient(135deg, var(--success-500), var(--success-600));
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--space-4) var(--space-8);
        font-weight: 600;
        font-size: var(--text-base);
        transition: all var(--transition-base);
        width: 100%;
    }

    .btn-register:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        background: linear-gradient(135deg, var(--success-600), var(--success-700));
    }

    .student-fields {
        background: var(--success-50);
        border: 2px solid var(--success-200);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        margin-top: var(--space-6);
    }

    @media (max-width: 768px) {
        .register-page {
            padding: var(--space-4);
        }

        .register-form-section,
        .register-info-section {
            padding: var(--space-8) var(--space-6);
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            padding: var(--space-3) var(--space-3);
            font-size: var(--text-sm);
        }

        .btn-register {
            padding: var(--space-3) var(--space-6);
            font-size: var(--text-sm);
        }
    }

    @media (max-width: 575.98px) {
        .register-page {
            padding: var(--space-3);
        }

        .register-form-section,
        .register-info-section {
            padding: var(--space-6) var(--space-4);
        }

        .register-title {
            font-size: var(--text-2xl);
        }

        .register-subtitle {
            font-size: var(--text-base);
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            padding: var(--space-3) var(--space-3);
            font-size: var(--text-sm);
        }

        .form-floating > label {
            font-size: var(--text-xs);
        }

        .btn-register {
            padding: var(--space-3) var(--space-4);
            font-size: var(--text-sm);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="register-page">
    <div class="register-container">
        <div class="register-card">
            <div class="row g-0">
                <!-- Info Section -->
                <div class="col-lg-5">
                    <div class="register-info-section">
                        <div class="position-relative z-2">
                            <h1 class="register-title">
                                <i class="bi bi-mortarboard me-3"></i>
                                Join EduManage
                            </h1>
                            <p class="register-subtitle">Create your account to access the comprehensive student management system.</p>

                            <div class="mb-4">
                                <h5 class="mb-3">What you'll get:</h5>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Access to academic records
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Real-time result updates
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Fee management system
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Secure data protection
                                    </li>
                                </ul>
                            </div>

                            <div class="text-center">
                                <p class="mb-2">Already have an account?</p>
                                <a href="{{ url_for('auth.login') }}" class="btn btn-outline-light">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Section -->
                <div class="col-lg-7">
                    <div class="register-form-section">
                        <div class="text-center mb-5">
                            <h2 class="h3 mb-2">Create Your Account</h2>
                            <p class="text-muted">Fill in your details to get started</p>
                        </div>

                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'info' if category == 'info' else 'success' }} alert-dismissible fade show" role="alert">
                                        {% if category == 'error' %}
                                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        {% elif category == 'warning' %}
                                            <i class="bi bi-exclamation-circle-fill me-2"></i>
                                        {% elif category == 'info' %}
                                            <i class="bi bi-info-circle-fill me-2"></i>
                                        {% else %}
                                            <i class="bi bi-check-circle-fill me-2"></i>
                                        {% endif %}
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <!-- Registration Form -->
                        <form method="POST" class="needs-validation" novalidate>
                            <!-- Basic Information -->
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="username" name="username"
                                               placeholder="Username" required minlength="3">
                                        <label for="username">
                                            <i class="bi bi-person me-2"></i>Username *
                                        </label>
                                        <div class="invalid-feedback">
                                            Username must be at least 3 characters long.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="role" name="role" required onchange="toggleStudentFields()">
                                            <option value="">Select Role</option>
                                            <option value="student">Student</option>
                                            <option value="teacher">Teacher</option>
                                            <option value="admin">Admin</option>
                                        </select>
                                        <label for="role">
                                            <i class="bi bi-shield me-2"></i>Role *
                                        </label>
                                        <div class="invalid-feedback">
                                            Please select a role.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="password" name="password"
                                               placeholder="Password" required minlength="6">
                                        <label for="password">
                                            <i class="bi bi-lock me-2"></i>Password *
                                        </label>
                                        <div class="invalid-feedback">
                                            Password must be at least 6 characters long.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                               placeholder="Confirm Password" required>
                                        <label for="confirm_password">
                                            <i class="bi bi-lock-fill me-2"></i>Confirm Password *
                                        </label>
                                        <div class="invalid-feedback">
                                            Full name is required.
                                        </div>
                                        <div class="invalid-feedback">
                                            Passwords do not match.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating">
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       placeholder="Full Name" required>
                                <label for="full_name">
                                    <i class="bi bi-person-badge me-2"></i>Full Name *
                                </label>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="email" name="email"
                                               placeholder="Email" required>
                                        <label for="email">
                                            <i class="bi bi-envelope me-2"></i>Email *
                                        </label>
                                        <div class="invalid-feedback">
                                            Please enter a valid email address.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               placeholder="Phone">
                                        <label for="phone">
                                            <i class="bi bi-telephone me-2"></i>Phone
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating">
                                <textarea class="form-control" id="address" name="address"
                                          placeholder="Address" style="height: 100px;"></textarea>
                                <label for="address">
                                    <i class="bi bi-geo-alt me-2"></i>Address
                                </label>
                            </div>

                            <!-- Student-specific fields -->
                            <div id="student-fields" class="student-fields" style="display: none;">
                                <h6 class="text-success mb-3">
                                    <i class="bi bi-mortarboard me-2"></i>Student Information
                                </h6>

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="student_id" name="student_id"
                                                   placeholder="Student ID" readonly>
                                            <label for="student_id">Student ID (Auto-generated)</label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="class_name" name="class_name"
                                                   placeholder="Class">
                                            <label for="class_name">Class (e.g., Grade 10)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid mt-5">
                                <button type="submit" class="btn btn-register">
                                    <i class="bi bi-person-plus me-2"></i>Create Account
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Toggle student fields
    function toggleStudentFields() {
        const roleSelect = document.getElementById('role');
        const studentFields = document.getElementById('student-fields');

        if (roleSelect.value === 'student') {
            studentFields.style.display = 'block';
            // Auto-generate student ID
            const timestamp = Date.now().toString().slice(-4);
            document.getElementById('student_id').value = 'STU' + timestamp;
        } else {
            studentFields.style.display = 'none';
        }
    }

    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;

        if (password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Auto-generate username from full name
    document.getElementById('full_name').addEventListener('blur', function() {
        const usernameField = document.getElementById('username');
        if (!usernameField.value && this.value) {
            const username = this.value.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '')
                .substring(0, 15);
            usernameField.value = username;
        }
    });
</script>
{% endblock %}
