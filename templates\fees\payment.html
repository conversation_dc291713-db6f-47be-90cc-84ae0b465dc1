{% extends "layout.html" %}

{% block title %}Record Payment - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Record Payment Header -->
        <div class="page-header success">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title">
                            <i class="bi bi-check-circle"></i>
                            Record Payment
                        </h1>
                        <p class="page-subtitle">Process and record payment for student fee with payment method and transaction details.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions">
                            <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Fees
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fee <PERSON> -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            Fee Details
                        </h2>
                    </div>

                    <div class="info-display">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Student Name</label>
                                    <div class="info-value">{{ fee.student_name or 'John Smith' }}</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Student ID</label>
                                    <div class="info-value">
                                        <span class="status-badge student">{{ fee.student_id or 'STU001' }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Fee Type</label>
                                    <div class="info-value">{{ fee.fee_type or 'Tuition Fee' }}</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Amount Due</label>
                                    <div class="info-value">
                                        <span class="amount-display">${{ "{:,.2f}".format(fee.amount or 2500.00) }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Due Date</label>
                                    <div class="info-value">
                                        <i class="bi bi-calendar me-2 text-muted"></i>{{ fee.due_date|date if fee.due_date else 'Dec 31, 2023' }}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-field">
                                    <label>Current Status</label>
                                    <div class="info-value">
                                        <span class="status-badge {{ 'pending' if (fee.status or 'pending') == 'pending' else 'unpaid' }}">
                                            <i class="bi bi-{{ 'clock' if (fee.status or 'pending') == 'pending' else 'x-circle' }}"></i>
                                            {{ (fee.status or 'pending').title() }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="bi bi-currency-dollar"></i>
                            Payment Information
                        </h2>
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row g-4 mb-5">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="payment_date" name="payment_date"
                                           placeholder="Payment Date" required>
                                    <label for="payment_date">
                                        <i class="bi bi-calendar me-2"></i>Payment Date *
                                    </label>
                                    <div class="invalid-feedback">
                                        Please select payment date.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="payment_method" name="payment_method" required>
                                        <option value="">Select Payment Method</option>
                                        <option value="Cash">Cash</option>
                                        <option value="Bank Transfer">Bank Transfer</option>
                                        <option value="Credit Card">Credit Card</option>
                                        <option value="Debit Card">Debit Card</option>
                                        <option value="Check">Check</option>
                                        <option value="Online Payment">Online Payment</option>
                                        <option value="Mobile Payment">Mobile Payment</option>
                                    </select>
                                    <label for="payment_method">
                                        <i class="bi bi-credit-card me-2"></i>Payment Method *
                                    </label>
                                    <div class="invalid-feedback">
                                        Please select payment method.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-5">
                            <textarea class="form-control" id="payment_notes" name="payment_notes"
                                      placeholder="Payment Notes" style="height: 100px;"></textarea>
                            <label for="payment_notes">
                                <i class="bi bi-chat-text me-2"></i>Payment Notes (Optional)
                            </label>
                        </div>

                        <!-- Payment Confirmation -->
                        <div class="alert alert-success mb-5">
                            <h6 class="alert-heading">
                                <i class="bi bi-check-circle me-2"></i>Payment Confirmation
                            </h6>
                            <p class="mb-2">
                                You are about to record a payment of <strong>${{ "{:,.2f}".format(fee.amount or 2500.00) }}</strong>
                                for <strong>{{ fee.fee_type or 'Tuition Fee' }}</strong> from <strong>{{ fee.student_name or 'John Smith' }}</strong>.
                            </p>
                            <p class="mb-0">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    This action will mark the fee as paid and update the financial records.
                                </small>
                            </p>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>Record Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
    .info-display {
        background: var(--gray-50);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
    }

    .info-field {
        margin-bottom: var(--space-4);
    }

    .info-field:last-child {
        margin-bottom: 0;
    }

    .info-field label {
        display: block;
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--gray-600);
        margin-bottom: var(--space-1);
    }

    .info-value {
        font-size: var(--text-base);
        font-weight: 500;
        color: var(--gray-900);
    }

    .amount-display {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--success-600);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Set default payment date to today
document.addEventListener('DOMContentLoaded', function() {
    const paymentDateField = document.getElementById('payment_date');
    if (paymentDateField) {
        paymentDateField.valueAsDate = new Date();
    }
});

// Form is handled by the global script.js - no additional validation needed
console.log('Fees payment form loaded - using global form validation');
</script>
{% endblock %}
{% endblock %}
