{% extends "layout.html" %}

{% block title %}Students Directory - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Students Header -->
        <div class="page-header success">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title">
                            <i class="bi bi-mortarboard"></i>
                            Students Directory
                        </h1>
                        <p class="page-subtitle">Comprehensive view of all enrolled students with their academic information and status.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions">
                            {% if current_user.role == 'admin' %}
                            <a href="{{ url_for('users.add_user') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus-fill me-2"></i>Add Student
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <form method="GET" class="row g-4">
                <div class="col-md-4">
                    <label for="search" class="form-label fw-semibold">Search Students</label>
                    <div class="search-box">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ search }}" placeholder="Search by name, ID, or email">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="class" class="form-label fw-semibold">Filter by Class</label>
                    <select class="form-select" id="class" name="class">
                        <option value="">All Classes</option>
                        <option value="Grade 10" {{ 'selected' if class_filter == 'Grade 10' }}>Grade 10</option>
                        <option value="Grade 11" {{ 'selected' if class_filter == 'Grade 11' }}>Grade 11</option>
                        <option value="Grade 12" {{ 'selected' if class_filter == 'Grade 12' }}>Grade 12</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label fw-semibold">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ 'selected' if status_filter == 'active' }}>Active</option>
                        <option value="inactive" {{ 'selected' if status_filter == 'inactive' }}>Inactive</option>
                        <option value="graduated" {{ 'selected' if status_filter == 'graduated' }}>Graduated</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end gap-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-2"></i>Apply Filters
                    </button>
                    <a href="{{ url_for('users.students_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-2"></i>Reset
                    </a>
                    <small class="text-muted ms-auto">{{ students|length or 0 }} students found</small>
                </div>
            </form>
        </div>

        <!-- Students Table -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="bi bi-table"></i>
                    Students Directory
                </h2>
                <div class="section-actions">
                    <span class="badge bg-success">{{ students|length or 0 }} Total Students</span>
                </div>
            </div>

            {% if students %}
            <div class="data-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Student ID</th>
                                <th>Class</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Admission</th>
                                {% if current_user.role == 'admin' %}
                                <th>Actions</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="d-inline-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px; background: linear-gradient(135deg, var(--success-500), var(--success-600)); border-radius: 50%; color: white; font-size: 0.9rem;">
                                                {{ student.full_name[0] if student.full_name else 'S' }}
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-semibold">{{ student.full_name }}</div>
                                            <small class="text-muted">{{ student.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge student">{{ student.student_id or 'STU001' }}</span>
                                </td>
                                <td>
                                    <div class="fw-semibold">{{ student.class_name or 'Grade 10' }}</div>
                                    <small class="text-muted">Section: {{ student.section or 'A' }}</small>
                                </td>
                                <td>
                                    <div>
                                        <i class="bi bi-telephone me-1 text-muted"></i>
                                        {{ student.phone or '+****************' }}
                                    </div>
                                    <div class="text-muted small">
                                        <i class="bi bi-envelope me-1"></i>
                                        {{ student.email }}
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge {{ 'active' if (student.status or 'active') == 'active' else 'inactive' if (student.status or 'active') == 'inactive' else 'pending' }}">
                                        <i class="bi bi-{{ 'check-circle' if (student.status or 'active') == 'active' else 'x-circle' if (student.status or 'active') == 'inactive' else 'clock' }}"></i>
                                        {{ (student.status or 'active').title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="text-muted small">{{ student.admission_date|date if student.admission_date else 'Sep 01, 2023' }}</div>
                                </td>
                                {% if current_user.role == 'admin' %}
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('users.edit_user', user_id=student.id) }}"
                                           class="action-btn edit" title="Edit Student">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('results.results_list', search=student.student_id) }}"
                                           class="action-btn view" title="View Results">
                                            <i class="bi bi-graph-up"></i>
                                        </a>
                                        <a href="{{ url_for('fees.fees_list', search=student.student_id) }}"
                                           class="action-btn view" title="View Fees">
                                            <i class="bi bi-currency-dollar"></i>
                                        </a>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-mortarboard" style="font-size: 4rem; color: var(--gray-400); margin-bottom: 2rem;"></i>
                <h3 style="color: var(--gray-600); margin-bottom: 1rem;">No students found</h3>
                <p style="color: var(--gray-500); margin-bottom: 2rem;">Try adjusting your search criteria or add new students.</p>
                {% if current_user.role == 'admin' %}
                <a href="{{ url_for('users.add_user') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus-fill me-2"></i>Add First Student
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
