{% extends "layout.html" %}

{% block title %}{{ student.full_name }} - Results - Student Result Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="bi bi-graph-up text-primary me-2"></i>
                    Academic Results
                </h1>
                <p class="text-muted mb-0">{{ student.full_name }} ({{ student.student_id }})</p>
            </div>
            <div>
                <a href="{{ url_for('results.results_list') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Back to All Results
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Student Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="avatar-circle mx-auto mb-2" style="width: 60px; height: 60px;">
                                <i class="bi bi-person-fill fs-3"></i>
                            </div>
                            <h5 class="mb-1">{{ student.full_name }}</h5>
                            <span class="badge bg-primary">{{ student.student_id }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-2">Class Information</h6>
                        <div class="fw-semibold">{{ student.class_name }}</div>
                        {% if student.section %}
                        <div class="text-muted">Section: {{ student.section }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-2">Academic Status</h6>
                        <span class="badge bg-{{ 'success' if student.status == 'active' else 'secondary' }}">
                            {{ student.status.title() }}
                        </span>
                        {% if student.admission_date %}
                        <div class="text-muted small mt-1">
                            Admitted: {{ student.admission_date|date }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-2">Overall Performance</h6>
                        {% if results %}
                        {% set total_percentage = results|map(attribute='percentage')|sum %}
                        {% set avg_percentage = total_percentage / results|length %}
                        <div class="fw-bold text-{{ 'success' if avg_percentage >= 80 else 'info' if avg_percentage >= 70 else 'warning' if avg_percentage >= 60 else 'danger' }}">
                            {{ "%.1f"|format(avg_percentage) }}%
                        </div>
                        <small class="text-muted">{{ results|length }} exam{{ 's' if results|length != 1 else '' }}</small>
                        {% else %}
                        <div class="text-muted">No results yet</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subject Performance Overview -->
{% if subject_performance %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart text-primary me-2"></i>
                    Subject Performance Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    {% for subject in subject_performance %}
                    <div class="col-md-6 col-lg-4">
                        <div class="card border h-100">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ subject.subject }}</h6>
                                <div class="mb-3">
                                    <div class="display-6 fw-bold text-{{ 'success' if subject.avg_percentage >= 80 else 'info' if subject.avg_percentage >= 70 else 'warning' if subject.avg_percentage >= 60 else 'danger' }}">
                                        {{ "%.1f"|format(subject.avg_percentage) }}%
                                    </div>
                                    <small class="text-muted">Average Score</small>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-{{ 'success' if subject.avg_percentage >= 80 else 'info' if subject.avg_percentage >= 70 else 'warning' if subject.avg_percentage >= 60 else 'danger' }}" 
                                         role="progressbar" 
                                         style="width: {{ subject.avg_percentage }}%">
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="text-success small">
                                            <i class="bi bi-arrow-up"></i>
                                            Best: {{ "%.1f"|format(subject.best_percentage) }}%
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-muted small">
                                            <i class="bi bi-list-ol"></i>
                                            {{ subject.total_exams }} exam{{ 's' if subject.total_exams != 1 else '' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Detailed Results Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table me-2"></i>Detailed Results
                    </h5>
                    {% if current_user.role in ['admin', 'teacher'] %}
                    <a href="{{ url_for('results.add_result') }}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Add Result
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body p-0">
                {% if results %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Subject</th>
                                <th>Exam Type</th>
                                <th>Date</th>
                                <th>Marks</th>
                                <th>Percentage</th>
                                <th>Grade</th>
                                <th>Teacher</th>
                                {% if current_user.role in ['admin', 'teacher'] %}
                                <th>Actions</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in results %}
                            <tr>
                                <td>
                                    <div class="fw-semibold">{{ result.subject }}</div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ result.exam_type }}</span>
                                </td>
                                <td>
                                    <div class="text-muted">{{ result.exam_date|date }}</div>
                                </td>
                                <td>
                                    <div class="fw-semibold">{{ result.marks_obtained }}/{{ result.total_marks }}</div>
                                </td>
                                <td>
                                    <div class="fw-bold text-{{ 'success' if result.percentage >= 80 else 'info' if result.percentage >= 70 else 'warning' if result.percentage >= 60 else 'danger' }}">
                                        {{ "%.1f"|format(result.percentage) }}%
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if result.grade in ['A+', 'A'] else 'info' if result.grade in ['B+', 'B'] else 'warning' if result.grade in ['C+', 'C'] else 'danger' }} fs-6">
                                        {{ result.grade or 'N/A' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="text-muted small">{{ result.teacher_name or 'N/A' }}</div>
                                </td>
                                {% if current_user.role in ['admin', 'teacher'] %}
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('results.edit_result', result_id=result.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Edit Result">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('results.delete_result', result_id=result.id) }}" 
                                              class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-outline-danger btn-delete" 
                                                    title="Delete Result" 
                                                    data-confirm="Are you sure you want to delete this result?">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-graph-up fs-1 text-muted d-block mb-3"></i>
                    <h5 class="text-muted">No results found</h5>
                    <p class="text-muted">No examination results have been recorded for this student yet.</p>
                    {% if current_user.role in ['admin', 'teacher'] %}
                    <a href="{{ url_for('results.add_result') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Add First Result
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Grade Distribution Chart -->
{% if results %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart text-primary me-2"></i>
                    Grade Distribution
                </h5>
            </div>
            <div class="card-body">
                {% set grade_counts = {} %}
                {% for result in results %}
                    {% if result.grade %}
                        {% set _ = grade_counts.update({result.grade: grade_counts.get(result.grade, 0) + 1}) %}
                    {% endif %}
                {% endfor %}
                
                {% if grade_counts %}
                <div class="row g-3">
                    {% for grade, count in grade_counts.items() %}
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="display-6 fw-bold text-{{ 'success' if grade in ['A+', 'A'] else 'info' if grade in ['B+', 'B'] else 'warning' if grade in ['C+', 'C'] else 'danger' }}">
                                {{ grade }}
                            </div>
                            <div class="text-muted">{{ count }} time{{ 's' if count != 1 else '' }}</div>
                            <div class="progress mt-2" style="height: 4px;">
                                <div class="progress-bar bg-{{ 'success' if grade in ['A+', 'A'] else 'info' if grade in ['B+', 'B'] else 'warning' if grade in ['C+', 'C'] else 'danger' }}" 
                                     style="width: {{ (count / results|length * 100) }}%"></div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <p>No grade data available</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .progress {
        border-radius: 10px;
        overflow: hidden;
    }
    
    .progress-bar {
        transition: width 0.6s ease;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Delete confirmation
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const confirmMessage = this.getAttribute('data-confirm');
            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
