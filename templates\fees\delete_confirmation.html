<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Fee Record Confirmation - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-danger me-2 px-3 py-2">Delete Fee</span>
                            <span class="d-none d-sm-inline">{{ fee.fee_type }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/fees/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Fees
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title text-danger">
                            <i class="bi bi-exclamation-triangle me-3"></i>
                            Delete Fee Record Confirmation
                        </h1>
                        <p class="page-subtitle">This action will permanently delete the fee record and any payment history. This cannot be undone.</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Warning Alert -->
                    <div class="alert alert-warning border-warning" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-3 fs-2"></i>
                            <div>
                                <h5 class="alert-heading mb-2">⚠️ FINANCIAL RECORD DELETION WARNING</h5>
                                <p class="mb-0">You are about to permanently delete this fee record and any associated payment history. This action cannot be undone!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Fee Information -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-currency-dollar me-2"></i>
                                Fee Record to be Deleted
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">Fee Details</h6>
                                    <p><strong>Student:</strong> {{ fee.student_name }}</p>
                                    <p><strong>Student ID:</strong> {{ fee.student_id }}</p>
                                    <p><strong>Fee Type:</strong> {{ fee.fee_type }}</p>
                                    <p><strong>Amount:</strong> ${{ "%.2f"|format(fee.amount) }}</p>
                                    <p><strong>Due Date:</strong> {{ fee.due_date.strftime('%Y-%m-%d') if fee.due_date else 'N/A' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-danger mb-3">Payment Status</h6>
                                    <p><strong>Status:</strong> 
                                        <span class="badge bg-{{ 'success' if fee.status == 'paid' else 'warning' if fee.status == 'partial' else 'danger' }}">
                                            {{ fee.status.title() }}
                                        </span>
                                    </p>
                                    {% if fee.paid_date %}
                                        <p><strong>Paid Date:</strong> {{ fee.paid_date.strftime('%Y-%m-%d') }}</p>
                                    {% endif %}
                                    {% if fee.payment_method %}
                                        <p><strong>Payment Method:</strong> {{ fee.payment_method.title() }}</p>
                                    {% endif %}
                                    <p><strong>Created:</strong> {{ fee.created_at.strftime('%Y-%m-%d') if fee.created_at else 'N/A' }}</p>
                                </div>
                            </div>
                            {% if fee.remarks %}
                            <div class="mt-3">
                                <h6 class="text-muted">Remarks:</h6>
                                <p class="text-muted">{{ fee.remarks }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Confirmation Form -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 text-danger">
                                <i class="bi bi-shield-exclamation me-2"></i>
                                Confirm Deletion
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6 class="alert-heading">What will be deleted:</h6>
                                <ul class="mb-0">
                                    <li>Fee record for <strong>{{ fee.student_name }}</strong></li>
                                    <li>Fee Type: <strong>{{ fee.fee_type }}</strong></li>
                                    <li>Amount: <strong>${{ "%.2f"|format(fee.amount) }}</strong></li>
                                    {% if fee.status == 'paid' %}
                                        <li><strong>Payment history</strong> (This fee was already paid!)</li>
                                    {% endif %}
                                    <li>All associated financial data</li>
                                </ul>
                            </div>

                            {% if fee.status == 'paid' %}
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">⚠️ IMPORTANT NOTICE:</h6>
                                <p class="mb-0">This fee has already been <strong>PAID</strong>. Deleting it will remove the payment record from the system. Consider carefully before proceeding.</p>
                            </div>
                            {% endif %}

                            <div class="row">
                                <div class="col-md-6">
                                    <form method="POST" action="/fees/delete/{{ fee.id }}" style="display: inline;">
                                        <button type="submit" class="btn btn-danger btn-lg w-100" 
                                                onclick="return confirm('Are you sure you want to delete this fee record?')">
                                            <i class="bi bi-trash me-2"></i>
                                            Yes, Delete Fee Record
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <a href="/fees/" class="btn btn-secondary btn-lg w-100">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        Cancel - Keep Record
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('🎯 THEMED: Fee delete confirmation page loaded');
        
        // Add confirmation for deletion
        document.querySelector('form').addEventListener('submit', function(e) {
            const confirmed = confirm('⚠️ Are you sure you want to delete this fee record? This action cannot be undone.');
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Deleting...';
                submitBtn.disabled = true;
            }
        });
    </script>
</body>
</html>
