{% extends "layout.html" %}

{% block title %}Teacher Assignments - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Teacher Assignments Header -->
        <div class="page-header bg-gradient-success">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title text-white mb-2">
                            <i class="bi bi-person-workspace me-3"></i>
                            Teacher Assignments
                        </h1>
                        <p class="page-subtitle text-white-75 mb-0">Manage teacher-subject assignments and class allocations for effective academic coordination.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions text-end">
                            <a href="{{ url_for('users.users_list') }}" class="btn btn-outline-light btn-lg me-3">
                                <i class="bi bi-arrow-left me-2"></i>Back to Users
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add New Assignment -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm rounded-3">
                    <div class="card-header bg-success-subtle rounded-top-3 border-bottom p-4">
                        <div class="d-flex align-items-center">
                            <h5 class="card-title mb-0 text-success">
                                <i class="bi bi-plus-circle me-2"></i>Create New Assignment
                            </h5>
                            <span class="badge bg-success-subtle text-success ms-auto px-3 py-2">
                                <i class="bi bi-lightning me-1"></i>Quick Add
                            </span>
                        </div>
                        <p class="text-muted mb-0 mt-2">Assign teachers to specific classes and subjects</p>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" action="{{ url_for('users.add_teacher_assignment') }}" class="row g-4">
                            <div class="col-lg-3 col-md-6">
                                <label for="teacher_id" class="form-label fw-bold text-dark mb-3">
                                    <i class="bi bi-person text-success me-2"></i>Teacher *
                                </label>
                                <select class="form-select form-select-lg border-2" id="teacher_id" name="teacher_id" required>
                                    <option value="">Choose teacher...</option>
                                    {% for teacher in teachers %}
                                    <option value="{{ teacher.id }}">👨‍🏫 {{ teacher.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-lg-3 col-md-6">
                                <label for="class_name" class="form-label fw-bold text-dark mb-3">
                                    <i class="bi bi-mortarboard text-success me-2"></i>Class *
                                </label>
                                <select class="form-select form-select-lg border-2" id="class_name" name="class_name" required>
                                    <option value="">Choose class...</option>
                                    {% for class in classes %}
                                    <option value="{{ class.class_name }}">🎓 {{ class.class_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <label for="section" class="form-label fw-bold text-dark mb-3">
                                    <i class="bi bi-grid text-success me-2"></i>Section
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text bg-light border-2">📝</span>
                                    <input type="text" class="form-control border-2" id="section" name="section" placeholder="A, B, C...">
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6">
                                <label for="subject" class="form-label fw-bold text-dark mb-3">
                                    <i class="bi bi-book text-success me-2"></i>Subject *
                                </label>
                                <select class="form-select form-select-lg border-2" id="subject" name="subject" required>
                                    <option value="">Choose subject...</option>
                                    {% for subject in subjects %}
                                    <option value="{{ subject.subject }}">
                                        {% if subject.subject == 'Mathematics' %}📐{% elif subject.subject == 'Physics' %}⚛️{% elif subject.subject == 'Chemistry' %}🧪{% elif subject.subject == 'English' %}📚{% elif subject.subject == 'Biology' %}🧬{% else %}📖{% endif %}
                                        {{ subject.subject }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-lg-1 col-md-12 d-flex align-items-end">
                                <button type="submit" class="btn btn-success btn-lg w-100 shadow-sm">
                                    <i class="bi bi-plus-circle me-2"></i>Add
                                </button>
                            </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Current Assignments -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table me-2"></i>Current Assignments
                </h5>
            </div>
            <div class="card-body p-0">
                {% if assignments %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Teacher</th>
                                <th>Class</th>
                                <th>Section</th>
                                <th>Subject</th>
                                <th>Assigned Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for assignment in assignments %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="bi bi-person-fill"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">{{ assignment.teacher_name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ assignment.class_name }}</span>
                                </td>
                                <td>
                                    {% if assignment.section %}
                                    <span class="badge bg-secondary">{{ assignment.section }}</span>
                                    {% else %}
                                    <span class="text-muted">All</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="fw-semibold">{{ assignment.subject }}</div>
                                </td>
                                <td>
                                    <small class="text-muted">{{ assignment.created_at|datetime }}</small>
                                </td>
                                <td>
                                    <form method="POST" action="{{ url_for('users.delete_teacher_assignment', assignment_id=assignment.id) }}" 
                                          class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger btn-delete" 
                                                title="Remove Assignment"
                                                data-confirm="Are you sure you want to remove this assignment for {{ assignment.teacher_name }}?">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-person-badge fs-1 text-muted d-block mb-3"></i>
                    <h5 class="text-muted">No teacher assignments found</h5>
                    <p class="text-muted">Add assignments above to specify which classes and subjects each teacher can manage.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Assignment Summary -->
{% if assignments %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart me-2"></i>Assignment Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 fw-bold text-primary">{{ teachers|length }}</div>
                            <div class="text-muted">Total Teachers</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 fw-bold text-success">{{ assignments|length }}</div>
                            <div class="text-muted">Total Assignments</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 fw-bold text-info">{{ classes|length }}</div>
                            <div class="text-muted">Classes</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 fw-bold text-warning">{{ subjects|length }}</div>
                            <div class="text-muted">Subjects</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Help Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>How Teacher Assignments Work
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="bi bi-check-circle me-2"></i>What Teachers Can Do
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-arrow-right text-muted me-2"></i>
                                Add and edit results only for assigned students
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-arrow-right text-muted me-2"></i>
                                View fee records (read-only access)
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-arrow-right text-muted me-2"></i>
                                Access student information for assigned classes
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">
                            <i class="bi bi-x-circle me-2"></i>What Teachers Cannot Do
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-arrow-right text-muted me-2"></i>
                                Add or edit results for unassigned students
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-arrow-right text-muted me-2"></i>
                                Manage fees (add, edit, or record payments)
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-arrow-right text-muted me-2"></i>
                                Access admin functions or user management
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <strong>Note:</strong> If a section is not specified, the teacher will be assigned to all sections of that class. 
                    To restrict to specific sections, enter the section name (e.g., "A", "B").
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Delete confirmation
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const confirmMessage = this.getAttribute('data-confirm');
            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
