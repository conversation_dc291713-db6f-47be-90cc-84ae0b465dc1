#!/usr/bin/env python3
"""
Financial Management Routes
Handles CRUD operations for student fees and payments
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from datetime import datetime, date
from routes.auth import login_required, teacher_required, fee_management_required
from models.db import get_db_connection, get_student_by_user_id

fees_bp = Blueprint('fees', __name__)

@fees_bp.route('/')
@login_required
def fees_list():
    """Display fees based on user role"""
    user_role = session.get('role')
    user_id = session.get('user_id')
    
    if user_role == 'student':
        return student_fees(user_id)
    else:
        return all_fees()

def all_fees():
    """Display all fees (for teachers and admins)"""
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    fee_type_filter = request.args.get('fee_type', '')
    class_filter = request.args.get('class', '')
    page = int(request.args.get('page', 1))
    per_page = 15
    
    conn = get_db_connection()
    
    # Build query with filters
    query = '''
        SELECT f.*, s.student_id, s.class_name, s.section, u.full_name as student_name,
               CASE 
                   WHEN f.status = 'pending' AND f.due_date < DATE('now') THEN 'overdue'
                   ELSE f.status
               END as display_status,
               (julianday('now') - julianday(f.due_date)) as days_overdue
        FROM financial_records f
        JOIN students s ON f.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE 1=1
    '''
    params = []
    
    if search:
        query += ' AND (u.full_name LIKE ? OR s.student_id LIKE ? OR f.fee_type LIKE ?)'
        search_param = f'%{search}%'
        params.extend([search_param, search_param, search_param])
    
    if status_filter:
        if status_filter == 'overdue':
            query += ' AND f.status = "pending" AND f.due_date < DATE("now")'
        else:
            query += ' AND f.status = ?'
            params.append(status_filter)
    
    if fee_type_filter:
        query += ' AND f.fee_type = ?'
        params.append(fee_type_filter)
    
    if class_filter:
        query += ' AND s.class_name = ?'
        params.append(class_filter)
    
    # Count total records
    count_query = query.replace(
        'SELECT f.*, s.student_id, s.class_name, s.section, u.full_name as student_name, CASE WHEN f.status = \'pending\' AND f.due_date < DATE(\'now\') THEN \'overdue\' ELSE f.status END as display_status, (julianday(\'now\') - julianday(f.due_date)) as days_overdue',
        'SELECT COUNT(*)'
    )
    total_fees = conn.execute(count_query, params).fetchone()[0]
    
    # Add pagination and ordering
    query += ' ORDER BY f.due_date DESC, u.full_name LIMIT ? OFFSET ?'
    params.extend([per_page, (page - 1) * per_page])
    
    fees = conn.execute(query, params).fetchall()
    
    # Get filter options
    fee_types = conn.execute('SELECT DISTINCT fee_type FROM financial_records ORDER BY fee_type').fetchall()
    classes = conn.execute('SELECT DISTINCT class_name FROM students ORDER BY class_name').fetchall()
    
    # Update overdue status in database
    conn.execute('''
        UPDATE financial_records 
        SET status = 'overdue' 
        WHERE status = 'pending' AND due_date < DATE('now')
    ''')
    conn.commit()
    
    conn.close()
    
    # Calculate pagination info
    total_pages = (total_fees + per_page - 1) // per_page
    
    return render_template('fees/list.html',
                         fees=fees,
                         fee_types=fee_types,
                         classes=classes,
                         search=search,
                         status_filter=status_filter,
                         fee_type_filter=fee_type_filter,
                         class_filter=class_filter,
                         page=page,
                         total_pages=total_pages,
                         total_fees=total_fees)

def student_fees(user_id):
    """Display fees for a specific student"""
    conn = get_db_connection()
    
    # Get student record
    student = get_student_by_user_id(user_id)
    if not student:
        flash('Student record not found.', 'error')
        return redirect(url_for('dashboard.dashboard'))
    
    # Get all fees for this student
    fees = conn.execute('''
        SELECT *,
               CASE 
                   WHEN status = 'pending' AND due_date < DATE('now') THEN 'overdue'
                   ELSE status
               END as display_status,
               (julianday('now') - julianday(due_date)) as days_overdue
        FROM financial_records
        WHERE student_id = ?
        ORDER BY due_date DESC
    ''', (student['id'],)).fetchall()
    
    # Get fee summary
    fee_summary = conn.execute('''
        SELECT 
            COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
            COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
            COALESCE(SUM(CASE WHEN status = 'pending' AND due_date < DATE('now') THEN amount ELSE 0 END), 0) as overdue_amount,
            COALESCE(SUM(amount), 0) as total_amount
        FROM financial_records 
        WHERE student_id = ?
    ''', (student['id'],)).fetchone()
    
    conn.close()
    
    return render_template('fees/student_fees.html',
                         student=student,
                         fees=fees,
                         fee_summary=fee_summary)

@fees_bp.route('/add-standalone', methods=['GET', 'POST'])
@login_required
def add_fee_standalone():
    """Add new fee record - STANDALONE VERSION"""
    print(f"\n🔥 STANDALONE FEES ADD! Method: {request.method}")

    if request.method == 'POST':
        print(f"📝 Fees form data: {dict(request.form)}")

        try:
            # Get form data
            student_id = request.form.get('student_id')
            fee_type = request.form.get('fee_type').strip()
            amount = float(request.form.get('amount'))
            due_date = datetime.strptime(request.form.get('due_date'), '%Y-%m-%d').date()
            remarks = request.form.get('remarks', '').strip()

            # Create fee record using direct database connection
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO fees (student_id, fee_type, amount, due_date, remarks, status)
                VALUES (?, ?, ?, ?, ?, 'pending')
            ''', (student_id, fee_type, amount, due_date, remarks))

            conn.commit()
            fee_id = cursor.lastrowid

            print(f"✅ STANDALONE: Fee record added successfully with ID: {fee_id}")
            flash(f'✅ Fee record added successfully for {fee_type}!', 'success')
            return redirect(url_for('fees.list_fees'))

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ STANDALONE: Error adding fee: {str(e)}")
            flash(f'❌ Error adding fee: {str(e)}', 'error')
        finally:
            if conn:
                conn.close()

    # Get students for dropdown
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT u.id, u.full_name, u.username
            FROM users u
            WHERE u.role = 'student'
            ORDER BY u.full_name
        ''')
        students = [{'id': row[0], 'full_name': row[1], 'username': row[2]} for row in cursor.fetchall()]
    except Exception as e:
        print(f"Error fetching students: {e}")
        students = []
    finally:
        if conn:
            conn.close()

    return render_template('fees/add_standalone.html', students=students)

@fees_bp.route('/add', methods=['GET', 'POST'])
@login_required
@fee_management_required
def add_fee():
    """Add new fee record"""
    if request.method == 'POST':
        student_id = request.form['student_id']
        fee_type = request.form['fee_type'].strip()
        amount = request.form['amount']
        due_date = request.form['due_date']
        remarks = request.form.get('remarks', '').strip()
        
        # Validation
        errors = []
        
        if not student_id:
            errors.append('Please select a student.')
        
        if not fee_type:
            errors.append('Fee type is required.')
        
        try:
            amount = float(amount)
            if amount <= 0:
                errors.append('Amount must be greater than 0.')
        except ValueError:
            errors.append('Please enter a valid amount.')
        
        if not due_date:
            errors.append('Due date is required.')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('fees/add.html', students=get_students_list())
        
        # Save fee record
        conn = get_db_connection()
        try:
            conn.execute('''
                INSERT INTO financial_records (student_id, fee_type, amount, due_date, remarks, status)
                VALUES (?, ?, ?, ?, ?, 'pending')
            ''', (student_id, fee_type, amount, due_date, remarks))
            conn.commit()
            flash('Fee record added successfully!', 'success')
            return redirect(url_for('fees.fees_list'))
        except Exception as e:
            conn.rollback()
            flash(f'Failed to add fee record: {str(e)}', 'error')
        finally:
            conn.close()
    
    students = get_students_list()
    return render_template('fees/add.html', students=students)

@fees_bp.route('/edit/<int:fee_id>', methods=['GET', 'POST'])
@login_required
@fee_management_required
def edit_fee(fee_id):
    """Edit existing fee record"""
    conn = get_db_connection()
    
    # Get fee data
    fee = conn.execute('''
        SELECT f.*, s.student_id, u.full_name as student_name
        FROM financial_records f
        JOIN students s ON f.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE f.id = ?
    ''', (fee_id,)).fetchone()
    
    if not fee:
        flash('Fee record not found.', 'error')
        return redirect(url_for('fees.fees_list'))
    
    if request.method == 'POST':
        fee_type = request.form['fee_type'].strip()
        amount = request.form['amount']
        due_date = request.form['due_date']
        remarks = request.form.get('remarks', '').strip()
        
        # Validation
        errors = []
        
        if not fee_type:
            errors.append('Fee type is required.')
        
        try:
            amount = float(amount)
            if amount <= 0:
                errors.append('Amount must be greater than 0.')
        except ValueError:
            errors.append('Please enter a valid amount.')
        
        if not due_date:
            errors.append('Due date is required.')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            conn.close()
            return render_template('fees/edit.html', fee=fee)
        
        # Update fee record
        try:
            conn.execute('''
                UPDATE financial_records 
                SET fee_type = ?, amount = ?, due_date = ?, remarks = ?
                WHERE id = ?
            ''', (fee_type, amount, due_date, remarks, fee_id))
            conn.commit()
            flash('Fee record updated successfully!', 'success')
            return redirect(url_for('fees.fees_list'))
        except Exception as e:
            conn.rollback()
            flash(f'Failed to update fee record: {str(e)}', 'error')
        finally:
            conn.close()
    
    conn.close()
    return render_template('fees/edit.html', fee=fee)

@fees_bp.route('/pay/<int:fee_id>', methods=['GET', 'POST'])
@login_required
@fee_management_required
def record_payment(fee_id):
    """Record payment for a fee"""
    conn = get_db_connection()
    
    # Get fee data
    fee = conn.execute('''
        SELECT f.*, s.student_id, u.full_name as student_name
        FROM financial_records f
        JOIN students s ON f.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE f.id = ?
    ''', (fee_id,)).fetchone()
    
    if not fee:
        flash('Fee record not found.', 'error')
        return redirect(url_for('fees.fees_list'))
    
    if fee['status'] == 'paid':
        flash('This fee has already been paid.', 'info')
        return redirect(url_for('fees.fees_list'))
    
    if request.method == 'POST':
        payment_date = request.form['payment_date']
        payment_method = request.form['payment_method'].strip()
        
        # Validation
        errors = []
        
        if not payment_date:
            errors.append('Payment date is required.')
        
        if not payment_method:
            errors.append('Payment method is required.')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            conn.close()
            return render_template('fees/payment.html', fee=fee)
        
        # Record payment
        try:
            conn.execute('''
                UPDATE financial_records 
                SET paid_date = ?, payment_method = ?, status = 'paid'
                WHERE id = ?
            ''', (payment_date, payment_method, fee_id))
            conn.commit()
            flash('Payment recorded successfully!', 'success')
            return redirect(url_for('fees.fees_list'))
        except Exception as e:
            conn.rollback()
            flash(f'Failed to record payment: {str(e)}', 'error')
        finally:
            conn.close()
    
    conn.close()
    return render_template('fees/payment.html', fee=fee)

@fees_bp.route('/delete/<int:fee_id>', methods=['POST'])
@login_required
@fee_management_required
def delete_fee(fee_id):
    """Delete fee record"""
    conn = get_db_connection()
    
    # Get fee data
    fee = conn.execute('SELECT * FROM financial_records WHERE id = ?', (fee_id,)).fetchone()
    if not fee:
        flash('Fee record not found.', 'error')
        return redirect(url_for('fees.fees_list'))
    
    try:
        conn.execute('DELETE FROM financial_records WHERE id = ?', (fee_id,))
        conn.commit()
        flash('Fee record deleted successfully!', 'success')
    except Exception as e:
        conn.rollback()
        flash(f'Failed to delete fee record: {str(e)}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('fees.fees_list'))

@fees_bp.route('/reports')
@login_required
@teacher_required
def financial_reports():
    """Display financial reports"""
    conn = get_db_connection()
    
    # Overall statistics
    stats = conn.execute('''
        SELECT 
            COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
            COALESCE(SUM(CASE WHEN status = 'pending' AND due_date < DATE('now') THEN amount ELSE 0 END), 0) as overdue_amount,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'pending' AND due_date < DATE('now') THEN 1 END) as overdue_count
        FROM financial_records
    ''').fetchone()
    
    # Fee type breakdown
    fee_type_breakdown = conn.execute('''
        SELECT fee_type,
               COUNT(*) as total_records,
               COALESCE(SUM(amount), 0) as total_amount,
               COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
               COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount
        FROM financial_records
        GROUP BY fee_type
        ORDER BY total_amount DESC
    ''').fetchall()
    
    # Monthly revenue (last 12 months)
    monthly_revenue = conn.execute('''
        SELECT strftime('%Y-%m', paid_date) as month,
               COALESCE(SUM(amount), 0) as revenue
        FROM financial_records
        WHERE status = 'paid' AND paid_date >= DATE('now', '-12 months')
        GROUP BY strftime('%Y-%m', paid_date)
        ORDER BY month
    ''').fetchall()
    
    # Class-wise fee collection
    class_breakdown = conn.execute('''
        SELECT s.class_name,
               COUNT(f.id) as total_records,
               COALESCE(SUM(f.amount), 0) as total_amount,
               COALESCE(SUM(CASE WHEN f.status = 'paid' THEN f.amount ELSE 0 END), 0) as paid_amount,
               COALESCE(SUM(CASE WHEN f.status = 'pending' THEN f.amount ELSE 0 END), 0) as pending_amount
        FROM students s
        LEFT JOIN financial_records f ON s.id = f.student_id
        GROUP BY s.class_name
        ORDER BY s.class_name
    ''').fetchall()
    
    conn.close()
    
    return render_template('fees/reports.html',
                         stats=stats,
                         fee_type_breakdown=fee_type_breakdown,
                         monthly_revenue=monthly_revenue,
                         class_breakdown=class_breakdown)

def get_students_list():
    """Get list of active students for dropdowns"""
    conn = get_db_connection()
    students = conn.execute('''
        SELECT s.id, s.student_id, u.full_name, s.class_name, s.section
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.status = 'active'
        ORDER BY u.full_name
    ''').fetchall()
    conn.close()
    return students
