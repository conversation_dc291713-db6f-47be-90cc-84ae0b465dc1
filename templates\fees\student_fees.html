{% extends "layout.html" %}

{% block title %}{{ student.full_name }} - Fee Records - Student Result Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="bi bi-currency-dollar text-primary me-2"></i>
                    Fee Records
                </h1>
                <p class="text-muted mb-0">{{ student.full_name }} ({{ student.student_id }})</p>
            </div>
            <div>
                <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Back to All Fees
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Student Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="avatar-circle mx-auto mb-2" style="width: 60px; height: 60px;">
                                <i class="bi bi-person-fill fs-3"></i>
                            </div>
                            <h5 class="mb-1">{{ student.full_name }}</h5>
                            <span class="badge bg-primary">{{ student.student_id }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-2">Class Information</h6>
                        <div class="fw-semibold">{{ student.class_name }}</div>
                        {% if student.section %}
                        <div class="text-muted">Section: {{ student.section }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-2">Contact</h6>
                        {% if student.email %}
                        <div class="small">
                            <i class="bi bi-envelope me-1"></i>{{ student.email }}
                        </div>
                        {% endif %}
                        {% if student.phone %}
                        <div class="small">
                            <i class="bi bi-telephone me-1"></i>{{ student.phone }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-2">Status</h6>
                        <span class="badge bg-{{ 'success' if student.status == 'active' else 'secondary' }}">
                            {{ student.status.title() }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fee Summary Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-success fs-2 mb-2">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
                <h4 class="text-success mb-1">${{ "%.2f"|format(fee_summary.paid_amount) }}</h4>
                <div class="text-muted">Total Paid</div>
                <small class="text-success">{{ fee_summary.paid_count }} payment{{ 's' if fee_summary.paid_count != 1 else '' }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-warning fs-2 mb-2">
                    <i class="bi bi-clock-fill"></i>
                </div>
                <h4 class="text-warning mb-1">${{ "%.2f"|format(fee_summary.pending_amount) }}</h4>
                <div class="text-muted">Pending</div>
                <small class="text-warning">{{ fee_summary.pending_count }} pending</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-danger fs-2 mb-2">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                </div>
                <h4 class="text-danger mb-1">${{ "%.2f"|format(fee_summary.overdue_amount) }}</h4>
                <div class="text-muted">Overdue</div>
                <small class="text-danger">{{ fee_summary.overdue_count }} overdue</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-primary fs-2 mb-2">
                    <i class="bi bi-calculator-fill"></i>
                </div>
                <h4 class="text-primary mb-1">${{ "%.2f"|format(fee_summary.total_amount) }}</h4>
                <div class="text-muted">Total Amount</div>
                <small class="text-muted">All time</small>
            </div>
        </div>
    </div>
</div>

<!-- Fee Records Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table me-2"></i>Fee Records
                    </h5>
                    {% if current_user.role in ['admin', 'teacher'] %}
                    <a href="{{ url_for('fees.add_fee') }}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Add Fee
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body p-0">
                {% if fees %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Fee Type</th>
                                <th>Amount</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Payment Date</th>
                                <th>Payment Method</th>
                                {% if current_user.role in ['admin', 'teacher'] %}
                                <th>Actions</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for fee in fees %}
                            <tr class="{{ 'table-danger' if fee.status == 'overdue' else 'table-warning' if fee.status == 'pending' else '' }}">
                                <td>
                                    <div class="fw-semibold">{{ fee.fee_type }}</div>
                                    {% if fee.remarks %}
                                    <small class="text-muted">{{ fee.remarks }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="fw-bold text-primary">${{ "%.2f"|format(fee.amount) }}</div>
                                </td>
                                <td>
                                    <div>{{ fee.due_date|date }}</div>
                                    {% if fee.status == 'overdue' %}
                                    <small class="text-danger">
                                        <i class="bi bi-exclamation-triangle me-1"></i>Overdue
                                    </small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if fee.status == 'paid' else 'danger' if fee.status == 'overdue' else 'warning' }} fs-6">
                                        {{ fee.status.title() }}
                                    </span>
                                </td>
                                <td>
                                    {% if fee.paid_date %}
                                    <div class="text-success">{{ fee.paid_date|date }}</div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if fee.payment_method %}
                                    <span class="badge bg-light text-dark">{{ fee.payment_method }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                {% if current_user.role in ['admin', 'teacher'] %}
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if fee.status != 'paid' %}
                                        <a href="{{ url_for('fees.record_payment', fee_id=fee.id) }}" 
                                           class="btn btn-sm btn-outline-success" title="Record Payment">
                                            <i class="bi bi-check-circle"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('fees.edit_fee', fee_id=fee.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Edit Fee">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('fees.delete_fee', fee_id=fee.id) }}" 
                                              class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-outline-danger btn-delete" 
                                                    title="Delete Fee" 
                                                    data-confirm="Are you sure you want to delete this fee record?">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-currency-dollar fs-1 text-muted d-block mb-3"></i>
                    <h5 class="text-muted">No fee records found</h5>
                    <p class="text-muted">No fee records have been created for this student yet.</p>
                    {% if current_user.role in ['admin', 'teacher'] %}
                    <a href="{{ url_for('fees.add_fee') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Add First Fee Record
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Delete confirmation
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const confirmMessage = this.getAttribute('data-confirm');
            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
