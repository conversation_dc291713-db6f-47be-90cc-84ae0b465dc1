{% extends "layout.html" %}

{% block title %}Add Student Result - EduManage{% endblock %}

{% block content %}
<!-- Add Result Header -->
<div class="page-header bg-gradient-info mb-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title text-white mb-2">
                    <i class="bi bi-plus-circle me-3"></i>
                    Add Student Result
                </h1>
                <p class="page-subtitle text-white-75 mb-0">Enter examination results and academic performance data for students with grades and assessments.</p>
            </div>
            <div class="col-lg-4 mt-4 mt-lg-0">
                <div class="page-actions text-end">
                    <a href="{{ url_for('results.results_list') }}" class="btn btn-light btn-lg shadow-sm">
                        <i class="bi bi-arrow-left me-2"></i>Back to Results
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

        <!-- Add Result Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="content-section bg-white rounded-3 shadow-sm">
                    <div class="section-header bg-light rounded-top-3 p-4 border-bottom">
                        <h2 class="section-title mb-0 text-dark">
                            <i class="bi bi-graph-up text-info me-2"></i>
                            Academic Result Information
                        </h2>
                        <p class="text-muted mb-0 mt-2">Enter examination details and scores for student assessment</p>
                    </div>

                    <div class="p-4">
                        <form method="POST" action="{{ url_for('results.add_result') }}">
                            <!-- Student and Subject Selection -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="student_id" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-person text-info me-2"></i>Select Student *
                                    </label>
                                    <select class="form-select form-select-lg border-2" id="student_id" name="student_id" required>
                                        <option value="">Choose a student...</option>
                                        {% for student in students %}
                                        <option value="{{ student.id }}">
                                            👨‍🎓 {{ student.full_name }} ({{ student.student_id }}) - {{ student.class_name or 'Grade 10' }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select a student.
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="subject" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-book text-info me-2"></i>Subject *
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">📚</span>
                                        <input type="text" class="form-control border-2" id="subject" name="subject"
                                               placeholder="e.g., Mathematics, Physics, Chemistry" required>
                                    </div>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Subject is required.
                                    </div>
                                </div>
                            </div>

                            <!-- Exam Type and Date -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="exam_type" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-clipboard-check text-info me-2"></i>Exam Type *
                                    </label>
                                    <select class="form-select form-select-lg border-2" id="exam_type" name="exam_type" required>
                                        <option value="">Choose exam type...</option>
                                        <option value="Midterm">📝 Midterm Examination</option>
                                        <option value="Final">🎓 Final Examination</option>
                                        <option value="Quiz">❓ Quiz</option>
                                        <option value="Assignment">📋 Assignment</option>
                                        <option value="Project">🔬 Project</option>
                                        <option value="Practical">🧪 Practical</option>
                                        <option value="Test">📄 Class Test</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select exam type.
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="exam_date" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-calendar text-info me-2"></i>Exam Date *
                                    </label>
                                    <input type="date" class="form-control form-control-lg border-2" id="exam_date" name="exam_date" required>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select exam date.
                                    </div>
                                </div>
                            </div>

                            <!-- Marks Section -->
                            <div class="marks-section bg-info-subtle rounded-3 p-4 mb-4">
                                <div class="d-flex align-items-center mb-4">
                                    <h5 class="mb-0 text-info">
                                        <i class="bi bi-award me-2"></i>Marks & Grading
                                    </h5>
                                    <span class="badge bg-info-subtle text-info ms-auto px-3 py-2">
                                        <i class="bi bi-calculator me-1"></i>Auto-calculated
                                    </span>
                                </div>

                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <label for="marks_obtained" class="form-label fw-bold text-dark mb-3">
                                            <i class="bi bi-award text-success me-2"></i>Marks Obtained *
                                        </label>
                                        <div class="input-group input-group-lg">
                                            <span class="input-group-text bg-light border-2">📊</span>
                                            <input type="number" class="form-control border-2" id="marks_obtained" name="marks_obtained"
                                                   placeholder="0" required min="0" step="0.5" onchange="calculatePercentage()">
                                        </div>
                                        <div class="invalid-feedback">
                                            <i class="bi bi-exclamation-circle me-1"></i>Please enter valid marks obtained.
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="total_marks" class="form-label fw-bold text-dark mb-3">
                                            <i class="bi bi-trophy text-warning me-2"></i>Total Marks *
                                        </label>
                                        <div class="input-group input-group-lg">
                                            <span class="input-group-text bg-light border-2">🎯</span>
                                            <input type="number" class="form-control border-2" id="total_marks" name="total_marks"
                                                   placeholder="100" required min="1" step="0.5" onchange="calculatePercentage()">
                                        </div>
                                        <div class="invalid-feedback">
                                            <i class="bi bi-exclamation-circle me-1"></i>Please enter valid total marks.
                                        </div>
                                    </div>
                                </div>

                                <!-- Auto-calculated Results -->
                                <div class="row g-4 mt-3">
                                    <div class="col-md-6">
                                        <div class="result-display-enhanced">
                                            <div class="label">
                                                <i class="bi bi-percent me-2"></i>Percentage
                                            </div>
                                            <div class="value text-info" id="percentage_display">0.0%</div>
                                            <small class="text-muted">Auto-calculated</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="result-display-enhanced">
                                            <div class="label">
                                                <i class="bi bi-award me-2"></i>Grade
                                            </div>
                                            <div class="value text-success" id="grade_display">-</div>
                                            <small class="text-muted">Based on percentage</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-5 pt-4 border-top">
                                <a href="{{ url_for('results.results_list') }}" class="btn btn-outline-secondary btn-lg px-4">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-info btn-lg px-5 shadow-sm">
                                    <i class="bi bi-check-circle me-2"></i>Save Result
                                </button>
                                <!-- Debug: Simple submit button -->
                                <button type="submit" class="btn btn-warning btn-sm ms-2" formnovalidate>
                                    <i class="bi bi-bug me-1"></i>Test Submit (No Validation)
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block extra_css %}
<style>
    .marks-section {
        background: var(--info-50);
        border: 2px solid var(--info-200);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
    }

    .result-display {
        background: var(--gray-100);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        text-align: center;
    }

    .result-display label {
        display: block;
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--gray-600);
        margin-bottom: var(--space-2);
    }

    .result-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--info-600);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Calculate grade and percentage
function calculatePercentage() {
    const marksObtained = parseFloat(document.getElementById('marks_obtained').value) || 0;
    const totalMarks = parseFloat(document.getElementById('total_marks').value) || 0;

    if (totalMarks > 0) {
        const percentage = (marksObtained / totalMarks) * 100;
        let grade = 'F';
        let gradeColor = 'var(--danger-600)';

        if (percentage >= 90) {
            grade = 'A+';
            gradeColor = 'var(--success-600)';
        } else if (percentage >= 80) {
            grade = 'A';
            gradeColor = 'var(--success-600)';
        } else if (percentage >= 70) {
            grade = 'B+';
            gradeColor = 'var(--info-600)';
        } else if (percentage >= 60) {
            grade = 'B';
            gradeColor = 'var(--info-600)';
        } else if (percentage >= 50) {
            grade = 'C+';
            gradeColor = 'var(--warning-600)';
        } else if (percentage >= 40) {
            grade = 'C';
            gradeColor = 'var(--warning-600)';
        } else if (percentage >= 33) {
            grade = 'D';
            gradeColor = 'var(--danger-600)';
        }

        // Update display
        const percentageDisplay = document.getElementById('percentage_display');
        const gradeDisplay = document.getElementById('grade_display');

        if (percentageDisplay) {
            percentageDisplay.textContent = percentage.toFixed(1) + '%';
            percentageDisplay.style.color = gradeColor;
        }

        if (gradeDisplay) {
            gradeDisplay.textContent = grade;
            gradeDisplay.style.color = gradeColor;
        }
    } else {
        const percentageDisplay = document.getElementById('percentage_display');
        const gradeDisplay = document.getElementById('grade_display');

        if (percentageDisplay) percentageDisplay.textContent = '0%';
        if (gradeDisplay) gradeDisplay.textContent = '-';
    }
}

// Validation
document.addEventListener('DOMContentLoaded', function() {
    const marksObtainedField = document.getElementById('marks_obtained');
    const totalMarksField = document.getElementById('total_marks');
    const examDateField = document.getElementById('exam_date');

    if (marksObtainedField) {
        marksObtainedField.addEventListener('input', function() {
            const marksObtained = parseFloat(this.value) || 0;
            const totalMarks = parseFloat(totalMarksField.value) || 0;

            if (totalMarks > 0 && marksObtained > totalMarks) {
                this.setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    if (totalMarksField) {
        totalMarksField.addEventListener('input', function() {
            const marksObtained = parseFloat(marksObtainedField.value) || 0;
            const totalMarks = parseFloat(this.value) || 0;

            if (totalMarks > 0 && marksObtained > totalMarks) {
                marksObtainedField.setCustomValidity('Marks obtained cannot exceed total marks');
            } else {
                marksObtainedField.setCustomValidity('');
            }
        });
    }

    // Set default date to today
    if (examDateField) {
        examDateField.valueAsDate = new Date();
    }
});

// Form is handled by the global script.js - no additional validation needed
console.log('Results add form loaded - using global form validation');
</script>
{% endblock %}
