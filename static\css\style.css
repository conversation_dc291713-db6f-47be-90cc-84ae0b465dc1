/* EduManage - Professional Student Management System */

/* Design System - Color Palette & Variables */
:root {
    /* Primary Brand Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors - Enhanced for Better Contrast */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --info-50: #f0f9ff;
    --info-100: #e0f2fe;
    --info-200: #bae6fd;
    --info-500: #06b6d4;
    --info-600: #0891b2;
    --info-700: #0e7490;

    /* Spacing Scale */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */

    /* Typography Scale */
    --text-xs: 0.75rem;   /* 12px */
    --text-sm: 0.875rem;  /* 14px */
    --text-base: 1rem;    /* 16px */
    --text-lg: 1.125rem;  /* 18px */
    --text-xl: 1.25rem;   /* 20px */
    --text-2xl: 1.5rem;   /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem;  /* 36px */

    /* Border Radius */
    --radius-sm: 0.375rem;  /* 6px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */

    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Reset & Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: var(--text-sm);
    font-weight: 400;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
    border: none;
    background: none;
    font-family: inherit;
    cursor: pointer;
}

/* Remove default link styles */
a {
    color: inherit;
    text-decoration: none;
}

/* Remove default list styles */
ul, ol {
    list-style: none;
}

/* Remove default form styles */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
}

/* Focus styles */
:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Selection styles */
::selection {
    background-color: var(--primary-100);
    color: var(--primary-900);
}

/* Header & Navigation */
.header-main {
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    background: white;
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

.top-bar {
    background: var(--gray-900) !important;
    padding: var(--space-3) 0;
    font-size: var(--text-xs);
    font-weight: 500;
}

.top-bar .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.top-bar .row {
    margin: 0;
}

.top-bar .col-md-6 {
    padding: 0;
}

.top-bar .d-flex {
    flex-wrap: wrap;
    gap: var(--space-2);
}

.navbar {
    padding: var(--space-5) 0;
    background: white !important;
    transition: all var(--transition-base);
}

.navbar .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    font-weight: 700;
    color: var(--gray-900) !important;
    text-decoration: none !important;
    padding: 0;
    margin-right: var(--space-8);
}

.brand-logo {
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-600);
    border-radius: var(--radius-xl);
    color: white;
    font-size: var(--text-2xl);
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.brand-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 1.2;
}

.brand-text h4 {
    margin: 0;
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--primary-600);
    line-height: 1.1;
}

.brand-text small {
    font-size: var(--text-xs);
    color: var(--gray-500);
    font-weight: 500;
    margin-top: var(--space-1);
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.navbar-toggler {
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-nav {
    gap: var(--space-1);
    align-items: center;
}

.navbar-nav .nav-link {
    font-weight: 500;
    font-size: var(--text-sm);
    color: var(--gray-600) !important;
    padding: var(--space-3) var(--space-4) !important;
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    white-space: nowrap;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-600) !important;
    background-color: var(--primary-50) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link i {
    font-size: var(--text-sm);
    width: 16px;
    text-align: center;
}

.dropdown-menu {
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-xl);
    padding: var(--space-3);
    margin-top: var(--space-2);
    min-width: 240px;
    background: white;
}

.dropdown-item {
    padding: var(--space-3) var(--space-4);
    font-weight: 500;
    font-size: var(--text-sm);
    color: var(--gray-700);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-1);
}

.dropdown-item:last-child {
    margin-bottom: 0;
}

.dropdown-item:hover {
    background-color: var(--primary-50);
    color: var(--primary-600);
    transform: translateX(2px);
}

.dropdown-item i {
    width: 18px;
    font-size: var(--text-base);
    text-align: center;
}

.dropdown-divider {
    margin: var(--space-3) 0;
    border-color: var(--gray-200);
}

.dropdown-item-text {
    padding: var(--space-3) var(--space-4);
    color: var(--gray-700);
}

.dropdown-item-text .fw-semibold {
    font-size: var(--text-sm);
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.dropdown-item-text small {
    font-size: var(--text-xs);
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-dropdown {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    border: 1px solid transparent;
}

.user-dropdown:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-200);
}

.user-avatar {
    width: 42px;
    height: 42px;
    border-radius: var(--radius-lg);
    background: var(--primary-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    flex-shrink: 0;
}

.user-info {
    line-height: 1.3;
    min-width: 0;
}

.user-info .fw-semibold {
    font-size: var(--text-sm);
    color: var(--gray-900);
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-info small {
    font-size: var(--text-xs);
    color: var(--gray-500);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Header Responsive Styles */
@media (max-width: 991.98px) {
    .top-bar {
        padding: var(--space-2) 0;
    }

    .top-bar .d-flex {
        justify-content: center;
        text-align: center;
    }

    .top-bar .col-md-6:last-child {
        margin-top: var(--space-2);
    }

    .navbar {
        padding: var(--space-4) 0;
    }

    .navbar-brand {
        margin-right: auto;
        gap: var(--space-3);
    }

    .brand-logo {
        width: 44px;
        height: 44px;
        font-size: var(--text-xl);
    }

    .brand-text h4 {
        font-size: var(--text-xl);
    }

    .navbar-collapse {
        margin-top: var(--space-4);
        padding-top: var(--space-4);
        border-top: 1px solid var(--gray-200);
    }

    .navbar-nav {
        gap: var(--space-2);
        width: 100%;
    }

    .navbar-nav .nav-link {
        padding: var(--space-3) var(--space-4) !important;
        justify-content: flex-start;
        width: 100%;
    }

    .user-dropdown {
        width: 100%;
        justify-content: flex-start;
        padding: var(--space-3) var(--space-4);
    }

    .dropdown-menu {
        width: 100%;
        margin-top: var(--space-1);
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
    }
}

@media (max-width: 767.98px) {
    .top-bar .container {
        padding: 0 var(--space-3);
    }

    .navbar .container {
        padding: 0 var(--space-3);
    }

    .top-bar .row {
        flex-direction: column;
        gap: var(--space-2);
        text-align: center;
    }

    .top-bar .col-md-6 {
        width: 100%;
    }

    .top-bar .col-md-6:last-child {
        margin-top: 0;
    }

    .brand-logo {
        width: 40px;
        height: 40px;
        font-size: var(--text-lg);
    }

    .brand-text h4 {
        font-size: var(--text-lg);
    }

    .brand-text small {
        font-size: 10px;
    }
}

@media (max-width: 575.98px) {
    .top-bar {
        display: none;
    }

    .navbar {
        padding: var(--space-3) 0;
    }

    .navbar-brand {
        gap: var(--space-2);
    }

    .brand-text small {
        display: none;
    }
}

/* Card Styles */
.card {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    overflow: hidden;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--primary-200);
}

.card-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6);
    font-weight: 600;
    color: var(--gray-900);
}

.card-body {
    padding: var(--space-6);
}

.card-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.card-text {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Button Styles */
.btn {
    font-weight: 500;
    font-size: var(--text-sm);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-5);
    transition: all var(--transition-base);
    border: 1px solid transparent;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    cursor: pointer;
    line-height: 1.5;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
}

.btn-primary:hover {
    background: var(--primary-700);
    border-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-600);
    color: white;
    border-color: var(--success-600);
}

.btn-success:hover {
    background: var(--success-700);
    border-color: var(--success-700);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success:focus {
    background: var(--success-600);
    border-color: var(--success-600);
    color: white;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.25);
}

.btn-warning {
    background: var(--warning-600);
    color: white;
    border-color: var(--warning-600);
}

.btn-warning:hover {
    background: var(--warning-700);
    border-color: var(--warning-700);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning:focus {
    background: var(--warning-600);
    border-color: var(--warning-600);
    color: white;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.25);
}

.btn-danger {
    background: var(--danger-600);
    color: white;
    border-color: var(--danger-600);
}

.btn-danger:hover {
    background: var(--danger-700);
    border-color: var(--danger-700);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger:focus {
    background: var(--danger-600);
    border-color: var(--danger-600);
    color: white;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.25);
}

.btn-info {
    background: var(--info-600);
    color: white;
    border-color: var(--info-600);
}

.btn-info:hover {
    background: var(--info-700);
    border-color: var(--info-700);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-info:focus {
    background: var(--info-600);
    border-color: var(--info-600);
    color: white;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.25);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-outline-primary:hover {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary:focus {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gray-600);
    border-color: var(--gray-400);
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    color: white;
    border-color: var(--gray-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-secondary:focus {
    background: transparent;
    color: var(--gray-600);
    border-color: var(--gray-600);
    box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.25);
}

.btn-outline-success {
    background: transparent;
    color: var(--success-600);
    border-color: var(--success-600);
}

.btn-outline-success:hover {
    background: var(--success-600);
    color: white;
    border-color: var(--success-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-warning {
    background: transparent;
    color: var(--warning-600);
    border-color: var(--warning-600);
}

.btn-outline-warning:hover {
    background: var(--warning-600);
    color: white;
    border-color: var(--warning-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-danger {
    background: transparent;
    color: var(--danger-600);
    border-color: var(--danger-600);
}

.btn-outline-danger:hover {
    background: var(--danger-600);
    color: white;
    border-color: var(--danger-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-info {
    background: transparent;
    color: var(--info-600);
    border-color: var(--info-600);
}

.btn-outline-info:hover {
    background: var(--info-600);
    color: white;
    border-color: var(--info-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-light {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover {
    background: white;
    color: var(--gray-900);
    border-color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Enhanced Form Styles - Professional Input Design */
.form-control, .form-select {
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-4);
    font-size: var(--text-base);
    font-weight: 500;
    background: white;
    color: var(--gray-900);
    transition: all var(--transition-base);
    line-height: 1.5;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.form-control:hover, .form-select:hover {
    border-color: var(--gray-400);
}

.form-control::placeholder {
    color: var(--gray-500);
    opacity: 1;
}

.form-control:disabled, .form-select:disabled {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-500);
    cursor: not-allowed;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.form-label i {
    color: var(--primary-600);
    font-size: var(--text-sm);
}

/* Floating Label Enhancements */
.form-floating > .form-control,
.form-floating > .form-select {
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-4);
    font-size: var(--text-base);
    transition: all var(--transition-base);
    background: white;
}

.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-select:not(:placeholder-shown) {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating > label {
    color: var(--gray-600);
    font-weight: 500;
    font-size: var(--text-sm);
    padding: var(--space-4);
    transition: all var(--transition-base);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select:focus ~ label,
.form-floating > .form-select:not(:placeholder-shown) ~ label {
    color: var(--primary-600);
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Form Validation States */
.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--success-500);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 1.38 1.38L7.7 4.09l.94.94L4.25 9.42z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid:focus,
.form-select.is-valid:focus {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--danger-500);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.valid-feedback {
    color: var(--success-600);
    font-size: var(--text-sm);
    font-weight: 500;
    margin-top: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.invalid-feedback {
    color: var(--danger-600);
    font-size: var(--text-sm);
    font-weight: 500;
    margin-top: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.valid-feedback::before {
    content: "✓";
    color: var(--success-600);
    font-weight: 700;
}

.invalid-feedback::before {
    content: "⚠";
    color: var(--danger-600);
    font-weight: 700;
}

/* Table Styles */
.table {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(37, 99, 235, 0.05);
}

.table tbody td {
    padding: 1rem;
    border-color: var(--border-color);
    vertical-align: middle;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
}

/* Enhanced Alert Styles - Better Color Contrast */
.alert {
    border: 1px solid transparent;
    border-radius: var(--radius-xl);
    padding: var(--space-5) var(--space-6);
    font-weight: 500;
    font-size: var(--text-sm);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.alert-success {
    background: var(--success-50);
    color: var(--success-700);
    border-color: var(--success-200);
}

.alert-success .bi {
    color: var(--success-600);
}

.alert-danger {
    background: var(--danger-50);
    color: var(--danger-700);
    border-color: var(--danger-200);
}

.alert-danger .bi {
    color: var(--danger-600);
}

.alert-warning {
    background: var(--warning-50);
    color: var(--warning-700);
    border-color: var(--warning-200);
}

.alert-warning .bi {
    color: var(--warning-600);
}

.alert-info {
    background: var(--info-50);
    color: var(--info-700);
    border-color: var(--info-200);
}

.alert-info .bi {
    color: var(--info-600);
}

.alert-dismissible .btn-close {
    padding: var(--space-2);
    margin: -var(--space-2) -var(--space-3) -var(--space-2) auto;
    background: transparent;
    border: none;
    opacity: 0.7;
    transition: opacity var(--transition-base);
}

.alert-dismissible .btn-close:hover {
    opacity: 1;
}

/* Progress Bar Styles */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius);
    background-color: var(--border-color);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    transition: width 0.6s ease;
}

/* Avatar Styles */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.8rem;
}

/* Footer Styles */
.footer-main {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-16) 0 var(--space-8);
    border-top: 1px solid var(--gray-800);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.footer-brand .brand-logo {
    width: 48px;
    height: 48px;
    background: var(--primary-600);
    color: white;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
}

.footer-brand .brand-text h5 {
    margin: 0;
    color: white;
    font-size: var(--text-xl);
    font-weight: 700;
}

.footer-brand .brand-text small {
    color: var(--gray-400);
    font-size: var(--text-sm);
}

.footer-description {
    color: var(--gray-400);
    font-size: var(--text-sm);
    line-height: 1.6;
    margin: 0;
}

.footer-heading {
    color: white;
    font-size: var(--text-base);
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: var(--space-2);
}

.footer-links a {
    color: var(--gray-400);
    font-size: var(--text-sm);
    text-decoration: none;
    transition: color var(--transition-base);
}

.footer-links a:hover {
    color: var(--primary-400);
}

.footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-contact li {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
    color: var(--gray-400);
    font-size: var(--text-sm);
}

.footer-contact i {
    color: var(--primary-400);
    margin-top: 2px;
    flex-shrink: 0;
}

.footer-social {
    display: flex;
    gap: var(--space-3);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    color: var(--gray-400);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-base);
    font-size: var(--text-lg);
}

.social-link:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--space-6);
    margin-top: var(--space-12);
}

.footer-bottom p {
    color: var(--gray-400);
    font-size: var(--text-sm);
    margin: 0;
}

.footer-legal {
    display: flex;
    gap: var(--space-6);
}

.footer-legal a {
    color: var(--gray-400);
    font-size: var(--text-sm);
    text-decoration: none;
    transition: color var(--transition-base);
}

.footer-legal a:hover {
    color: var(--primary-400);
}

/* ========================================
   UNIFIED PAGE SYSTEM - PROFESSIONAL DESIGN
   ======================================== */

/* Page Container System */
.page-container {
    background: var(--gray-50);
    min-height: calc(100vh - 160px);
    padding: var(--space-8) 0 var(--space-12);
}

.page-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* Page Header System */
.page-header {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-12) var(--space-10);
    margin-bottom: var(--space-12);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.page-header.success {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
}

.page-header.warning {
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
}

.page-header.info {
    background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%);
}

.page-header.bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.page-header.bg-gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.page-header.bg-gradient-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

/* Text color utilities */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: var(--text-4xl);
    font-weight: 700;
    margin-bottom: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    line-height: 1.2;
}

.page-title i {
    font-size: var(--text-3xl);
    opacity: 0.9;
}

.page-subtitle {
    font-size: var(--text-lg);
    opacity: 0.9;
    margin: 0;
    line-height: 1.5;
    font-weight: 400;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-top: var(--space-8);
    flex-wrap: wrap;
}

/* Content Card System */
.content-section {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-10);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--space-8);
    transition: all var(--transition-base);
}

.content-section:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-200);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-4);
    border-bottom: 2px solid var(--gray-100);
}

.section-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.section-title i {
    color: var(--primary-600);
    font-size: var(--text-xl);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* Filter & Search System */
.filter-section {
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
    border: 1px solid var(--gray-200);
}

.search-box {
    position: relative;
}

.search-box .search-icon {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: var(--text-base);
    z-index: 2;
}

.search-box .form-control {
    padding-left: var(--space-12);
}

/* Table System */
.data-table {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--space-8);
}

.table {
    margin: 0;
}

.table thead th {
    background: var(--gray-900);
    color: white;
    font-weight: 600;
    border: none;
    padding: var(--space-5) var(--space-4);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody td {
    padding: var(--space-4);
    border-color: var(--gray-200);
    vertical-align: middle;
    font-size: var(--text-sm);
    color: var(--gray-800);
}

.table tbody tr {
    transition: all var(--transition-base);
}

.table tbody tr:hover {
    background: var(--gray-50);
    transform: translateX(2px);
}

.table tbody tr:nth-child(even) {
    background: rgba(249, 250, 251, 0.5);
}

.table tbody tr:nth-child(even):hover {
    background: var(--gray-50);
}

/* Status Badges - Enhanced Contrast */
.status-badge {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    border: 1px solid transparent;
    transition: all var(--transition-base);
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.status-badge.active {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.status-badge.inactive {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
}

.status-badge.pending {
    background: var(--warning-100);
    color: var(--warning-700);
    border: 1px solid var(--warning-200);
}

.status-badge.paid {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.status-badge.unpaid {
    background: var(--danger-100);
    color: var(--danger-700);
    border: 1px solid var(--danger-200);
}

.status-badge.admin {
    background: var(--danger-100);
    color: var(--danger-700);
    border: 1px solid var(--danger-200);
}

.status-badge.teacher {
    background: var(--info-100);
    color: var(--info-700);
    border: 1px solid var(--info-200);
}

.status-badge.student {
    background: var(--warning-100);
    color: var(--warning-700);
    border: 1px solid var(--warning-200);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    transition: all var(--transition-base);
    border: 1px solid transparent;
    text-decoration: none;
}

.action-btn.edit {
    background: var(--primary-100);
    color: var(--primary-600);
    border-color: var(--primary-200);
}

.action-btn.edit:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-1px);
}

.action-btn.delete {
    background: var(--danger-100);
    color: var(--danger-600);
    border-color: var(--danger-200);
}

.action-btn.delete:hover {
    background: var(--danger-600);
    color: white;
    transform: translateY(-1px);
}

.action-btn.view {
    background: var(--info-100);
    color: var(--info-600);
    border-color: var(--info-200);
}

.action-btn.view:hover {
    background: var(--info-600);
    color: white;
    transform: translateY(-1px);
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: var(--space-8);
}

.pagination .page-link {
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    padding: var(--space-3) var(--space-4);
    margin: 0 var(--space-1);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
}

.pagination .page-link:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
    color: var(--primary-600);
}

.pagination .page-item.active .page-link {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

/* Responsive Design for Unified System */
@media (max-width: 1199.98px) {
    .page-content {
        padding: 0 var(--space-4);
    }
}

@media (max-width: 991.98px) {
    .page-header {
        padding: var(--space-8) var(--space-6);
        margin-bottom: var(--space-10);
    }

    .page-title {
        font-size: var(--text-3xl);
    }

    .page-actions {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-3);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 767.98px) {
    .page-container {
        padding: var(--space-6) 0 var(--space-10);
    }

    .page-content {
        padding: 0 var(--space-3);
    }

    .page-header {
        padding: var(--space-6) var(--space-4);
        margin-bottom: var(--space-8);
    }

    .page-title {
        font-size: var(--text-2xl);
        gap: var(--space-3);
    }

    .page-title i {
        font-size: var(--text-2xl);
    }

    .content-section {
        padding: var(--space-8);
    }

    .section-title {
        font-size: var(--text-xl);
    }

    .filter-section {
        padding: var(--space-6);
    }

    .table-responsive {
        border-radius: var(--radius-xl);
    }

    .action-buttons {
        flex-direction: column;
        gap: var(--space-1);
    }
}

@media (max-width: 575.98px) {
    .page-container {
        padding: var(--space-4) 0 var(--space-8);
    }

    .page-header {
        padding: var(--space-5) var(--space-3);
        margin-bottom: var(--space-6);
    }

    .page-title {
        font-size: var(--text-xl);
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .page-subtitle {
        font-size: var(--text-sm);
        text-align: center;
    }

    .content-section {
        padding: var(--space-6);
    }

    .section-title {
        font-size: var(--text-lg);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }

    .filter-section {
        padding: var(--space-4);
    }

    .action-btn {
        width: 32px;
        height: 32px;
        font-size: var(--text-xs);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Enhanced Badge Utilities */
.bg-primary-subtle {
    background-color: rgba(59, 130, 246, 0.1) !important;
}

.bg-success-subtle {
    background-color: rgba(16, 185, 129, 0.1) !important;
}

.bg-warning-subtle {
    background-color: rgba(245, 158, 11, 0.1) !important;
}

.bg-danger-subtle {
    background-color: rgba(239, 68, 68, 0.1) !important;
}

.bg-info-subtle {
    background-color: rgba(6, 182, 212, 0.1) !important;
}

.bg-secondary-subtle {
    background-color: rgba(107, 114, 128, 0.1) !important;
}

.bg-dark-subtle {
    background-color: rgba(31, 41, 55, 0.1) !important;
}

.bg-purple-subtle {
    background-color: rgba(147, 51, 234, 0.1) !important;
}

.bg-muted-subtle {
    background-color: rgba(156, 163, 175, 0.1) !important;
}

.text-primary {
    color: #3b82f6 !important;
}

.text-success {
    color: #10b981 !important;
}

.text-warning {
    color: #f59e0b !important;
}

.text-danger {
    color: #ef4444 !important;
}

.text-info {
    color: #06b6d4 !important;
}

.text-secondary {
    color: #6b7280 !important;
}

.text-purple {
    color: #9333ea !important;
}

.text-muted {
    color: #9ca3af !important;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
}

/* Icon Circle Utility */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 600;
}

/* Status Indicator Animations */
.status-indicator {
    transition: all 0.3s ease;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Enhanced Table Styling */
.table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.table tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.02) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Fee Type Badge Enhancements */
.badge {
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Enhanced Result Display for Forms */
.result-display-enhanced {
    background: rgba(59, 130, 246, 0.05);
    border: 2px solid rgba(59, 130, 246, 0.1);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.result-display-enhanced:hover {
    border-color: rgba(59, 130, 246, 0.2);
    background: rgba(59, 130, 246, 0.08);
}

.result-display-enhanced .label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.result-display-enhanced .value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

/* Password Toggle Button */
.btn-outline-secondary:hover {
    background-color: #f8f9fa;
    border-color: #6c757d;
}

/* Enhanced Form Controls */
.form-control:focus,
.form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

.form-control-lg,
.form-select-lg {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

/* Input Group Enhancements */
.input-group-text {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Progress Bar Enhancements */
.progress {
    border-radius: 0.5rem;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Border Color Utilities for Dashboard */
.border-left-success {
    border-left-color: #10b981 !important;
}

.border-left-warning {
    border-left-color: #f59e0b !important;
}

.border-left-danger {
    border-left-color: #ef4444 !important;
}

/* Dashboard Specific Enhancements */
.fw-black {
    font-weight: 900 !important;
}

.fee-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Status Badge Modern Styling */
.status-badge-modern {
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.status-badge-modern:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Enhanced Icon Circle */
.icon-circle {
    transition: all 0.3s ease;
}

.icon-circle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Improved Text Contrast */
.text-secondary {
    color: #4b5563 !important;
    font-weight: 500;
}

/* Enhanced Action Buttons */
.action-buttons .btn {
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Avatar Circle Enhancement */
.avatar-circle {
    transition: all 0.3s ease;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.avatar-circle:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Enhanced Form Validation Styles */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: var(--success-500);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 1.38 1.38L7.7 4.09l.94.94L4.25 9.42z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid:focus,
.form-control.is-valid:focus {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: var(--danger-500);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.was-validated .form-select:valid,
.form-select.is-valid {
    border-color: var(--success-500);
}

.was-validated .form-select:valid:focus,
.form-select.is-valid:focus {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.was-validated .form-select:invalid,
.form-select.is-invalid {
    border-color: var(--danger-500);
}

.was-validated .form-select:invalid:focus,
.form-select.is-invalid:focus {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Checkbox and Radio Validation */
.was-validated .form-check-input:valid,
.form-check-input.is-valid {
    border-color: var(--success-500);
}

.was-validated .form-check-input:valid:checked,
.form-check-input.is-valid:checked {
    background-color: var(--success-600);
    border-color: var(--success-600);
}

.was-validated .form-check-input:valid:focus,
.form-check-input.is-valid:focus {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.was-validated .form-check-input:invalid,
.form-check-input.is-invalid {
    border-color: var(--danger-500);
}

.was-validated .form-check-input:invalid:focus,
.form-check-input.is-invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-500);
}

/* ========================================
   ENHANCED ADD FORM STYLING SYSTEM
   ======================================== */

/* Form Page Headers with Color Themes */
.page-header.add-user {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.page-header.add-fee {
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
}

.page-header.add-result {
    background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%);
}

/* Enhanced Form Sections */
.form-section {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-10);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.form-section.fee-form::before {
    background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.form-section.result-form::before {
    background: linear-gradient(90deg, var(--info-500), var(--info-600));
}

/* Enhanced Form Groups */
.enhanced-form-group {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    transition: all var(--transition-base);
}

.enhanced-form-group:hover {
    border-color: var(--primary-300);
    background: white;
    box-shadow: var(--shadow-sm);
}

.enhanced-form-group.student-info {
    background: var(--primary-50);
    border-color: var(--primary-200);
}

.enhanced-form-group.student-info:hover {
    border-color: var(--primary-400);
}

.enhanced-form-group.fee-info {
    background: var(--warning-50);
    border-color: var(--warning-200);
}

.enhanced-form-group.fee-info:hover {
    border-color: var(--warning-400);
}

.enhanced-form-group.result-info {
    background: var(--info-50);
    border-color: var(--info-200);
}

.enhanced-form-group.result-info:hover {
    border-color: var(--info-400);
}

/* Form Group Headers */
.form-group-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-5);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid var(--gray-200);
}

.form-group-title {
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.form-group-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    color: white;
    background: var(--primary-600);
    box-shadow: var(--shadow-sm);
}

.form-group-icon.fee {
    background: var(--warning-600);
}

.form-group-icon.result {
    background: var(--info-600);
}

/* Enhanced Input Styling */
.form-floating.enhanced {
    margin-bottom: var(--space-5);
}

.form-floating.enhanced > .form-control,
.form-floating.enhanced > .form-select {
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-4);
    font-size: var(--text-base);
    font-weight: 500;
    background: white;
    transition: all var(--transition-base);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.form-floating.enhanced > .form-control:focus,
.form-floating.enhanced > .form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);
    background: white;
    transform: translateY(-1px);
}

.form-floating.enhanced > .form-control:hover,
.form-floating.enhanced > .form-select:hover {
    border-color: var(--gray-400);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.form-floating.enhanced > label {
    color: var(--gray-600);
    font-weight: 600;
    font-size: var(--text-sm);
    transition: all var(--transition-base);
}

.form-floating.enhanced > .form-control:focus ~ label,
.form-floating.enhanced > .form-control:not(:placeholder-shown) ~ label,
.form-floating.enhanced > .form-select:focus ~ label,
.form-floating.enhanced > .form-select:not(:placeholder-shown) ~ label {
    color: var(--primary-600);
    font-weight: 700;
}

/* Special Input Types */
.form-floating.enhanced.fee-input > .form-control:focus ~ label,
.form-floating.enhanced.fee-input > .form-select:focus ~ label {
    color: var(--warning-600);
}

.form-floating.enhanced.result-input > .form-control:focus ~ label,
.form-floating.enhanced.result-input > .form-select:focus ~ label {
    color: var(--info-600);
}

/* Enhanced Information Cards */
.info-card-enhanced {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    margin-bottom: var(--space-6);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.info-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
}

.info-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.info-item-enhanced {
    display: flex;
    align-items: flex-start;
    gap: var(--space-4);
    margin-bottom: var(--space-5);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
}

.info-item-enhanced:hover {
    background: var(--gray-50);
    transform: translateX(4px);
}

.info-item-enhanced:last-child {
    margin-bottom: 0;
}

.info-item-enhanced .icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
}

.info-item-enhanced .content {
    flex: 1;
    min-width: 0;
}

.info-item-enhanced .title {
    font-size: var(--text-base);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.3;
}

.info-item-enhanced .description {
    font-size: var(--text-sm);
    color: var(--gray-600);
    line-height: 1.5;
    margin: 0;
}

/* Result Display Components */
.result-display-enhanced {
    background: linear-gradient(135deg, white 0%, var(--gray-50) 100%);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    text-align: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.result-display-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--info-500), var(--info-600));
}

.result-display-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--info-300);
}

.result-display-enhanced .label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: var(--space-3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-display-enhanced .value {
    font-size: var(--text-3xl);
    font-weight: 800;
    color: var(--info-600);
    line-height: 1;
    margin-bottom: var(--space-2);
}

.result-display-enhanced .subtitle {
    font-size: var(--text-xs);
    color: var(--gray-500);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Form Actions */
.form-actions-enhanced {
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-top: var(--space-8);
    border: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.form-actions-enhanced .actions-left {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.form-actions-enhanced .actions-right {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* Enhanced Buttons for Forms */
.btn-enhanced {
    padding: var(--space-4) var(--space-6);
    font-weight: 600;
    font-size: var(--text-sm);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    border: 2px solid transparent;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    cursor: pointer;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced.primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border-color: var(--primary-600);
    box-shadow: var(--shadow-sm);
}

.btn-enhanced.primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-enhanced.secondary {
    background: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-enhanced.secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}
