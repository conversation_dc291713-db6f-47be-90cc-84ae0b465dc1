{% extends "layout.html" %}

{% block title %}Academic Results - EduManage{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background-color: #f8fafc; min-height: 100vh;">
    <!-- Results Header -->
    <div class="bg-info text-white rounded-3 p-4 mb-4 shadow-sm">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="h2 mb-2">
                    <i class="bi bi-graph-up me-2"></i>
                    Academic Results
                </h1>
                <p class="mb-0 opacity-75">View and manage student examination results and academic performance records.</p>
            </div>
            <div class="col-lg-4 mt-3 mt-lg-0 text-lg-end">
                {% if current_user.role in ['admin', 'teacher'] %}
                <a href="{{ url_for('results.add_result') }}" class="btn btn-light btn-lg shadow-sm">
                    <i class="bi bi-plus-circle me-2"></i>Add Result
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-3 shadow-sm p-4 mb-4">
        <div class="d-flex align-items-center mb-3">
            <h5 class="mb-0 text-dark">
                <i class="bi bi-funnel text-info me-2"></i>Filter Results
            </h5>
            <span class="badge bg-light text-dark ms-auto">{{ results|length or 0 }} results found</span>
        </div>
        <form method="GET" class="row g-3">
            <div class="col-lg-3 col-md-6">
                <label for="search" class="form-label fw-semibold text-dark">
                    <i class="bi bi-search me-1"></i>Search Results
                </label>
                <div class="position-relative">
                    <i class="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                    <input type="text" class="form-control ps-5" id="search" name="search"
                           value="{{ search }}" placeholder="Search by student name or subject">
                </div>
            </div>

                <div class="col-lg-2 col-md-6">
                    <label for="subject" class="form-label fw-semibold text-dark">
                        <i class="bi bi-book me-1"></i>Subject
                    </label>
                    <select class="form-select border-2" id="subject" name="subject">
                        <option value="">All Subjects</option>
                        <option value="Mathematics" {{ 'selected' if subject_filter == 'Mathematics' }}>📐 Mathematics</option>
                        <option value="Physics" {{ 'selected' if subject_filter == 'Physics' }}>⚛️ Physics</option>
                        <option value="Chemistry" {{ 'selected' if subject_filter == 'Chemistry' }}>🧪 Chemistry</option>
                        <option value="English" {{ 'selected' if subject_filter == 'English' }}>📚 English</option>
                        <option value="Biology" {{ 'selected' if subject_filter == 'Biology' }}>🧬 Biology</option>
                        <option value="History" {{ 'selected' if subject_filter == 'History' }}>🏛️ History</option>
                        <option value="Geography" {{ 'selected' if subject_filter == 'Geography' }}>🌍 Geography</option>
                    </select>
                </div>

                <div class="col-lg-2 col-md-6">
                    <label for="exam_type" class="form-label fw-semibold text-dark">
                        <i class="bi bi-clipboard-check me-1"></i>Exam Type
                    </label>
                    <select class="form-select border-2" id="exam_type" name="exam_type">
                        <option value="">All Types</option>
                        <option value="Midterm" {{ 'selected' if exam_type_filter == 'Midterm' }}>📝 Midterm</option>
                        <option value="Final" {{ 'selected' if exam_type_filter == 'Final' }}>🎓 Final</option>
                        <option value="Quiz" {{ 'selected' if exam_type_filter == 'Quiz' }}>❓ Quiz</option>
                        <option value="Assignment" {{ 'selected' if exam_type_filter == 'Assignment' }}>📋 Assignment</option>
                        <option value="Project" {{ 'selected' if exam_type_filter == 'Project' }}>🔬 Project</option>
                    </select>
                </div>

                <div class="col-lg-2 col-md-6">
                    <label for="grade" class="form-label fw-semibold text-dark">
                        <i class="bi bi-award me-1"></i>Grade
                    </label>
                    <select class="form-select border-2" id="grade" name="grade">
                        <option value="">All Grades</option>
                        <option value="A+" {{ 'selected' if grade_filter == 'A+' }}>🏆 A+</option>
                        <option value="A" {{ 'selected' if grade_filter == 'A' }}>🥇 A</option>
                        <option value="B+" {{ 'selected' if grade_filter == 'B+' }}>🥈 B+</option>
                        <option value="B" {{ 'selected' if grade_filter == 'B' }}>🥉 B</option>
                        <option value="C+" {{ 'selected' if grade_filter == 'C+' }}>📈 C+</option>
                        <option value="C" {{ 'selected' if grade_filter == 'C' }}>📊 C</option>
                        <option value="D" {{ 'selected' if grade_filter == 'D' }}>📉 D</option>
                        <option value="F" {{ 'selected' if grade_filter == 'F' }}>❌ F</option>
                    </select>
                </div>

                <div class="col-lg-3 col-md-12 d-flex align-items-end gap-2">
                    <button type="submit" class="btn btn-info px-4">
                        <i class="bi bi-funnel me-2"></i>Apply Filters
                    </button>
                    <a href="{{ url_for('results.results_list') }}" class="btn btn-outline-secondary px-4">
                        <i class="bi bi-arrow-clockwise me-2"></i>Reset
                    </a>
                </div>
            </form>
        </div>

    <!-- Results Table -->
    <div class="bg-white rounded-3 shadow-sm">
        <div class="bg-light rounded-top-3 p-4 border-bottom">
            <div class="d-flex align-items-center justify-content-between">
                <h2 class="mb-0 text-dark">
                    <i class="bi bi-table text-info me-2"></i>
                    Academic Results Directory
                </h2>
                <span class="badge bg-info fs-6 px-3 py-2">{{ results|length or 0 }} Total Results</span>
            </div>
        </div>

        {% if results %}
        <div class="table-responsive">
            <table class="table table-hover mb-0 align-middle">
                        <thead class="bg-info text-white">
                            <tr>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-person me-2"></i>Student
                                </th>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-book me-2"></i>Subject
                                </th>
                                <th class="fw-semibold border-0 py-3 d-none d-md-table-cell">
                                    <i class="bi bi-clipboard-check me-2"></i>Exam Type
                                </th>
                                <th class="fw-semibold border-0 py-3">
                                    <i class="bi bi-graph-up me-2"></i>Score
                                </th>
                                <th class="fw-semibold border-0 py-3 d-none d-lg-table-cell">
                                    <i class="bi bi-award me-2"></i>Grade
                                </th>
                                <th class="fw-semibold border-0 py-3 d-none d-lg-table-cell">
                                    <i class="bi bi-percent me-2"></i>Percentage
                                </th>
                                <th class="fw-semibold border-0 py-3 d-none d-xl-table-cell">
                                    <i class="bi bi-calendar me-2"></i>Date
                                </th>
                                {% if current_user.role in ['admin', 'teacher'] %}
                                <th class="fw-semibold border-0 py-3 text-center d-none d-md-table-cell">
                                    <i class="bi bi-gear me-2"></i>Actions
                                </th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in results %}
                            <tr class="border-bottom">
                                <td class="py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-info text-white d-flex align-items-center justify-content-center rounded-circle"
                                                 style="width: 40px; height: 40px; font-weight: 600; font-size: 0.9rem;">
                                                {{ result.student_name[0] if result.student_name else 'S' }}
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-semibold text-dark mb-1">{{ result.student_name or 'Unknown Student' }}</div>
                                            <small class="text-muted">
                                                <i class="bi bi-person-badge me-1"></i>ID: {{ result.student_id or 'N/A' }}
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    {% set subject = result.subject or 'Unknown' %}
                                    <span class="badge bg-info-subtle text-info px-3 py-2 rounded-pill fw-semibold">
                                        {% if subject == 'Mathematics' %}📐{% elif subject == 'Physics' %}⚛️{% elif subject == 'Chemistry' %}🧪{% elif subject == 'English' %}📚{% elif subject == 'Biology' %}🧬{% elif subject == 'History' %}🏛️{% elif subject == 'Geography' %}🌍{% else %}📖{% endif %}
                                        {{ subject }}
                                    </span>
                                </td>
                                <td class="py-3 d-none d-md-table-cell">
                                    {% set exam_type = result.exam_type or 'Unknown' %}
                                    <span class="badge bg-light text-dark px-3 py-2 rounded-pill fw-semibold">
                                        {% if exam_type == 'Midterm' %}📝{% elif exam_type == 'Final' %}🎓{% elif exam_type == 'Quiz' %}❓{% elif exam_type == 'Assignment' %}📋{% elif exam_type == 'Project' %}🔬{% else %}📄{% endif %}
                                        {{ exam_type }}
                                    </span>
                                </td>
                                <td class="py-3">
                                    <div class="text-center">
                                        <div class="fw-bold text-info fs-5 mb-1">{{ result.marks_obtained or 0 }}/{{ result.total_marks or 100 }}</div>
                                        <small class="text-muted">Points</small>
                                    </div>
                                </td>
                                <td class="py-3 d-none d-lg-table-cell">
                                    {% set grade = result.grade or 'N/A' %}
                                    {% if grade in ['A+', 'A'] %}
                                        <span class="badge bg-success text-white px-3 py-2 rounded-pill fw-semibold">
                                            {{ '🏆 ' if grade == 'A+' else '🥇 ' }}{{ grade }}
                                        </span>
                                    {% elif grade in ['B+', 'B'] %}
                                        <span class="badge bg-info text-white px-3 py-2 rounded-pill fw-semibold">
                                            {{ '🥈 ' if grade == 'B+' else '🥉 ' }}{{ grade }}
                                        </span>
                                    {% elif grade in ['C+', 'C'] %}
                                        <span class="badge bg-warning text-dark px-3 py-2 rounded-pill fw-semibold">
                                            📈 {{ grade }}
                                        </span>
                                    {% elif grade == 'D' %}
                                        <span class="badge bg-secondary text-white px-3 py-2 rounded-pill fw-semibold">
                                            📉 {{ grade }}
                                        </span>
                                    {% elif grade == 'F' %}
                                        <span class="badge bg-danger text-white px-3 py-2 rounded-pill fw-semibold">
                                            ❌ {{ grade }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-light text-muted px-3 py-2 rounded-pill fw-semibold">
                                            {{ grade }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="py-3 d-none d-lg-table-cell">
                                    {% set percentage = (result.marks_obtained / result.total_marks * 100) if result.total_marks else 0 %}
                                    <div class="text-center">
                                        <span class="badge bg-{{ 'success' if percentage >= 80 else 'warning' if percentage >= 60 else 'danger' }} text-white px-3 py-2 rounded-pill fw-semibold">
                                            {{ "%.1f"|format(percentage) }}%
                                        </span>
                                    </div>
                                </td>
                                <td class="py-3 d-none d-xl-table-cell">
                                    <div class="text-center">
                                        <div class="fw-semibold text-dark mb-1">{{ result.exam_date|date if result.exam_date else 'N/A' }}</div>
                                        {% if result.created_at %}
                                        <small class="text-muted">Added {{ result.created_at|time }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                {% if current_user.role in ['admin', 'teacher'] %}
                                <td class="py-3 d-none d-md-table-cell">
                                    <div class="d-flex gap-1 justify-content-center">
                                        <a href="{{ url_for('results.edit_result', result_id=result.id) if result.id else '#' }}"
                                           class="btn btn-sm btn-outline-info rounded-pill" title="Edit Result">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('results.delete_result', result_id=result.id) if result.id else '#' }}"
                                           class="btn btn-sm btn-outline-danger rounded-pill" title="Delete Result"
                                           onclick="return confirm('Are you sure you want to delete this result?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5 px-4">
                <div class="empty-state-icon mb-4">
                    <i class="bi bi-graph-up text-muted" style="font-size: 5rem; opacity: 0.5;"></i>
                </div>
                <h3 class="text-dark mb-3 fw-bold">No results found</h3>
                <p class="text-muted mb-4 fs-5">Try adjusting your search criteria or add new results to get started.</p>
                {% if current_user.role in ['admin', 'teacher'] %}
                <a href="{{ url_for('results.add_result') }}" class="btn btn-info btn-lg px-4 py-3 rounded-pill shadow-sm">
                    <i class="bi bi-plus-circle me-2"></i>Add First Result
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
</div>
{% endblock %}
