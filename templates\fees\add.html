{% extends "layout.html" %}

{% block title %}Add Fee Record - EduManage{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-content">
        <!-- Add Fee Header -->
        <div class="page-header bg-gradient-success">
            <div class="page-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title text-white mb-2">
                            <i class="bi bi-plus-circle me-3"></i>
                            Add Fee Record
                        </h1>
                        <p class="page-subtitle text-white-75 mb-0">Create a new fee record for a student with payment details and due dates.</p>
                    </div>
                    <div class="col-lg-4 mt-4 mt-lg-0">
                        <div class="page-actions text-end">
                            <a href="{{ url_for('fees.fees_list') }}" class="btn btn-light btn-lg shadow-sm">
                                <i class="bi bi-arrow-left me-2"></i>Back to Fees
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Fee Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="content-section bg-white rounded-3 shadow-sm">
                    <div class="section-header bg-light rounded-top-3 p-4 border-bottom">
                        <h2 class="section-title mb-0 text-dark">
                            <i class="bi bi-currency-dollar text-success me-2"></i>
                            Fee Information
                        </h2>
                        <p class="text-muted mb-0 mt-2">Fill in the details below to create a new fee record</p>
                    </div>

                    <div class="p-4">
                        <form method="POST">
                            <!-- Student Selection -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="student_id" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-person text-primary me-2"></i>Select Student *
                                    </label>
                                    <select class="form-select form-select-lg border-2" id="student_id" name="student_id" required>
                                        <option value="">Choose a student...</option>
                                        {% for student in students %}
                                        <option value="{{ student.id }}">
                                            👨‍🎓 {{ student.full_name }} ({{ student.student_id }}) - {{ student.class_name or 'Grade 10' }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select a student.
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="fee_type" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-tag text-primary me-2"></i>Fee Type *
                                    </label>
                                    <select class="form-select form-select-lg border-2" id="fee_type" name="fee_type" required>
                                        <option value="">Choose fee type...</option>
                                        <option value="Tuition">📚 Tuition Fee</option>
                                        <option value="Lab">🔬 Lab Fee</option>
                                        <option value="Library">📖 Library Fee</option>
                                        <option value="Sports">⚽ Sports Fee</option>
                                        <option value="Exam">📝 Exam Fee</option>
                                        <option value="Transport">🚌 Transport Fee</option>
                                        <option value="Hostel">🏠 Hostel Fee</option>
                                        <option value="Other">💰 Other</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select fee type.
                                    </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Amount and Due Date -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <label for="amount" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-currency-dollar text-success me-2"></i>Amount *
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-2">$</span>
                                        <input type="number" class="form-control border-2" id="amount" name="amount"
                                               placeholder="0.00" required min="0.01" step="0.01">
                                    </div>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please enter a valid amount.
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="due_date" class="form-label fw-bold text-dark mb-3">
                                        <i class="bi bi-calendar text-warning me-2"></i>Due Date *
                                    </label>
                                    <input type="date" class="form-control form-control-lg border-2" id="due_date" name="due_date" required>
                                    <div class="invalid-feedback">
                                        <i class="bi bi-exclamation-circle me-1"></i>Please select due date.
                                    </div>
                                </div>
                            </div>

                            <!-- Remarks -->
                            <div class="mb-4">
                                <label for="remarks" class="form-label fw-bold text-dark mb-3">
                                    <i class="bi bi-chat-text text-info me-2"></i>Remarks (Optional)
                                </label>
                                <textarea class="form-control form-control-lg border-2" id="remarks" name="remarks"
                                          placeholder="Add any additional notes or comments..." rows="4"></textarea>
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-5 pt-4 border-top">
                                <a href="{{ url_for('fees.fees_list') }}" class="btn btn-outline-secondary btn-lg px-4">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-success btn-lg px-5 shadow-sm">
                                    <i class="bi bi-check-circle me-2"></i>Create Fee Record
                                </button>
                            </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Fee Types Information -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-10">
                <div class="content-section bg-white rounded-3 shadow-sm">
                    <div class="section-header bg-light rounded-top-3 p-4 border-bottom">
                        <h2 class="section-title mb-0 text-dark">
                            <i class="bi bi-info-circle text-info me-2"></i>
                            Fee Types Guide
                        </h2>
                        <p class="text-muted mb-0 mt-2">Understanding different types of fees in the system</p>
                    </div>

                    <div class="p-4">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="info-card bg-light rounded-3 p-4 h-100">
                                    <div class="info-item d-flex align-items-start mb-4">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                📚
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Tuition Fee</strong>
                                            <p class="text-muted mb-0">Regular academic fees for classes and instruction</p>
                                        </div>
                                    </div>
                                    <div class="info-item d-flex align-items-start mb-4">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-info text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                🔬
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Lab Fee</strong>
                                            <p class="text-muted mb-0">Laboratory and practical session charges</p>
                                        </div>
                                    </div>
                                    <div class="info-item d-flex align-items-start mb-4">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-success text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                📖
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Library Fee</strong>
                                            <p class="text-muted mb-0">Library access and resource usage</p>
                                        </div>
                                    </div>
                                    <div class="info-item d-flex align-items-start">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-warning text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                ⚽
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Sports Fee</strong>
                                            <p class="text-muted mb-0">Sports and recreational activities</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card bg-light rounded-3 p-4 h-100">
                                    <div class="info-item d-flex align-items-start mb-4">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                📝
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Exam Fee</strong>
                                            <p class="text-muted mb-0">Examination and assessment charges</p>
                                        </div>
                                    </div>
                                    <div class="info-item d-flex align-items-start mb-4">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-danger text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                🚌
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Transport Fee</strong>
                                            <p class="text-muted mb-0">School transportation services</p>
                                        </div>
                                    </div>
                                    <div class="info-item d-flex align-items-start mb-4">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-dark text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                🏠
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Hostel Fee</strong>
                                            <p class="text-muted mb-0">Accommodation and boarding charges</p>
                                        </div>
                                    </div>
                                    <div class="info-item d-flex align-items-start">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="icon-circle bg-muted text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                                💰
                                            </div>
                                        </div>
                                        <div>
                                            <strong class="text-dark d-block mb-2">Other</strong>
                                            <p class="text-muted mb-0">Miscellaneous and custom fees</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
    .info-card {
        background: var(--gray-50);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
    }

    .info-item {
        display: flex;
        align-items-flex-start;
        gap: var(--space-4);
        margin-bottom: var(--space-4);
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-item i {
        font-size: 1.5rem;
        margin-top: var(--space-1);
    }

    .info-item strong {
        display: block;
        margin-bottom: var(--space-1);
        color: var(--gray-900);
    }

    .info-item p {
        margin: 0;
        color: var(--gray-600);
        font-size: var(--text-sm);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Set default due date to next month
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    const dueDateField = document.getElementById('due_date');
    if (dueDateField) {
        dueDateField.valueAsDate = nextMonth;
    }
});

// Form is handled by the global script.js - no additional validation needed
console.log('Fees add form loaded - using global form validation');
</script>
{% endblock %}
{% endblock %}
