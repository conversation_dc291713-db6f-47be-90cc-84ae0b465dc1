<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Fee Record - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-success me-2 px-3 py-2">Fees</span>
                            <span class="d-none d-sm-inline">Add Fee Record</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/fees/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Fees
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="bi bi-plus-circle-fill me-3"></i>
                            Add Fee Record
                        </h1>
                        <p class="page-subtitle">Create a new fee record for a student with payment details and due dates.</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-currency-dollar me-2"></i>
                                Fee Record Information
                            </h5>
                            <small class="opacity-75">Enter student fee details and payment information</small>
                        </div>
                    <div class="card-body p-5">
                        <!-- PURE HTML FORM - NO JAVASCRIPT -->
                        <form method="POST" action="/fees/add">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="student_id" class="form-label">
                                        <i class="bi bi-person me-1"></i>Select Student *
                                    </label>
                                    <select class="form-select" id="student_id" name="student_id" required>
                                        <option value="">Choose Student</option>
                                        {% for student in students %}
                                            <option value="{{ student.id }}">{{ student.full_name }} ({{ student.username }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="fee_type" class="form-label">
                                        <i class="bi bi-tag me-1"></i>Fee Type *
                                    </label>
                                    <select class="form-select" id="fee_type" name="fee_type" required>
                                        <option value="">Select Fee Type</option>
                                        <option value="Tuition">📚 Tuition Fee</option>
                                        <option value="Library">📖 Library Fee</option>
                                        <option value="Laboratory">🔬 Laboratory Fee</option>
                                        <option value="Sports">⚽ Sports Fee</option>
                                        <option value="Transport">🚌 Transport Fee</option>
                                        <option value="Examination">📝 Examination Fee</option>
                                        <option value="Other">📋 Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">
                                        <i class="bi bi-currency-dollar me-1"></i>Amount *
                                    </label>
                                    <input type="number" class="form-control" id="amount" name="amount" 
                                           required min="0" step="0.01" placeholder="0.00">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="due_date" class="form-label">
                                        <i class="bi bi-calendar me-1"></i>Due Date *
                                    </label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>Remarks
                                </label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Optional notes about this fee"></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-success btn-lg px-5">
                                    <i class="bi bi-plus-circle me-2"></i>Add Fee Record
                                </button>
                                <a href="/fees/" class="btn btn-outline-secondary btn-lg ms-3 px-5">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SIMPLE JAVASCRIPT - NO INTERFERENCE -->
    <script>
        console.log('🎯 STANDALONE: Fees add page loaded - pure HTML form');
        
        // Set default due date to next month
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
            const dueDateField = document.getElementById('due_date');
            if (dueDateField) {
                dueDateField.valueAsDate = nextMonth;
            }
        });

        // Simple form submission logging (NO INTERFERENCE)
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('🚀 STANDALONE: Fees form submitting to:', this.action);
            
            // Show loading on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Adding Fee...';
                submitBtn.disabled = true;
            }
            
            // NO preventDefault() - let it submit naturally!
        });

        console.log('✅ STANDALONE: Fees form ready for submission');
    </script>
</body>
</html>
