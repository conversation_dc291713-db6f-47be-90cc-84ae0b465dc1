#!/usr/bin/env python3
"""
User Management Routes
Handles CRUD operations for users (Admin only)
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from werkzeug.security import generate_password_hash
from routes.auth import login_required, admin_required
from models.db import get_db_connection, get_all_users, get_all_students, get_teacher_assignments

users_bp = Blueprint('users', __name__)

@users_bp.route('/')
@login_required
@admin_required
def users_list():
    """Display all users"""
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    page = int(request.args.get('page', 1))
    per_page = 10
    
    conn = get_db_connection()
    
    # Build query with filters
    query = 'SELECT * FROM users WHERE 1=1'
    params = []
    
    if search:
        query += ' AND (full_name LIKE ? OR username LIKE ? OR email LIKE ?)'
        search_param = f'%{search}%'
        params.extend([search_param, search_param, search_param])
    
    if role_filter:
        query += ' AND role = ?'
        params.append(role_filter)
    
    # Count total records
    count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
    total_users = conn.execute(count_query, params).fetchone()[0]
    
    # Add pagination
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?'
    params.extend([per_page, (page - 1) * per_page])
    
    users = conn.execute(query, params).fetchall()
    conn.close()
    
    # Calculate pagination info
    total_pages = (total_users + per_page - 1) // per_page
    
    return render_template('users/list.html', 
                         users=users,
                         search=search,
                         role_filter=role_filter,
                         page=page,
                         total_pages=total_pages,
                         total_users=total_users)

@users_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """Add new user"""
    print(f"\n{'='*60}")
    print(f"🔥 USER ADD ROUTE HIT! Method: {request.method}")
    print(f"🔥 URL: {request.url}")
    print(f"🔥 Headers: {dict(request.headers)}")
    print(f"{'='*60}\n")

    if request.method == 'POST':
        print(f"\n🎯 POST REQUEST RECEIVED!")
        print(f"📝 Form data: {dict(request.form)}")
        print(f"📝 Form keys: {list(request.form.keys())}")
        print(f"📝 Content type: {request.content_type}")
        print(f"📝 Content length: {request.content_length}")
        print(f"{'='*60}\n")
        
        # Get form data with proper validation
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        role = request.form.get('role', '')
        full_name = request.form.get('full_name', '').strip()
        email = request.form.get('email', '').strip()
        phone = request.form.get('phone', '').strip()
        address = request.form.get('address', '').strip()
        
        # Student-specific fields
        student_id = request.form.get('student_id', '').strip()
        class_name = request.form.get('class_name', '').strip()
        section = request.form.get('section', '').strip()
        parent_name = request.form.get('parent_name', '').strip()
        parent_phone = request.form.get('parent_phone', '').strip()
        
        # Validation
        errors = []
        
        if not username or len(username) < 3:
            errors.append('Username must be at least 3 characters long.')
        
        if not password or len(password) < 6:
            errors.append('Password must be at least 6 characters long.')
        
        if not full_name:
            errors.append('Full name is required.')
        
        if not email or '@' not in email:
            errors.append('Valid email is required.')
        
        if role not in ['admin', 'teacher', 'student']:
            errors.append('Invalid role selected.')
        
        if role == 'student':
            if not student_id:
                errors.append('Student ID is required for students.')
            if not class_name:
                errors.append('Class is required for students.')
        
        # Check for existing username/email
        conn = get_db_connection()
        try:
            existing_user = conn.execute(
                'SELECT id FROM users WHERE username = ? OR email = ?', (username, email)
            ).fetchone()
            
            if existing_user:
                errors.append('Username or email already exists.')
            
            # Check for existing student ID
            if role == 'student' and student_id:
                existing_student = conn.execute(
                    'SELECT id FROM students WHERE student_id = ?', (student_id,)
                ).fetchone()
                if existing_student:
                    errors.append('Student ID already exists.')
            
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('users/add.html')
            
            # Create user
            try:
                password_hash = generate_password_hash(password)
                cursor = conn.cursor()
                
                print(f"DEBUG: Creating user with data: {username}, {role}, {full_name}, {email}")
                cursor.execute('''
                    INSERT INTO users (username, password_hash, role, full_name, email, phone, address)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (username, password_hash, role, full_name, email, phone, address))
                
                user_id = cursor.lastrowid
                print(f"DEBUG: Created user with ID: {user_id}")
                
                # If student, create student record
                if role == 'student':
                    print(f"DEBUG: Creating student record for user {user_id}")
                    cursor.execute('''
                        INSERT INTO students (user_id, student_id, class_name, section, 
                                            admission_date, parent_name, parent_phone, status)
                        VALUES (?, ?, ?, ?, DATE('now'), ?, ?, 'active')
                    ''', (user_id, student_id, class_name, section, parent_name, parent_phone))
                    print(f"DEBUG: Created student record")
                
                conn.commit()
                print(f"DEBUG: Successfully created {role} user: {full_name}")
                flash(f'{role.title()} "{full_name}" created successfully!', 'success')
                return redirect(url_for('users.users_list'))
                
            except Exception as e:
                conn.rollback()
                print(f"DEBUG: Database error during user creation: {str(e)}")
                flash(f'Failed to create user: {str(e)}', 'error')
                return render_template('users/add.html')
                
        except Exception as e:
            print(f"DEBUG: Error during validation: {str(e)}")
            flash(f'An error occurred: {str(e)}', 'error')
            return render_template('users/add.html')
        finally:
            conn.close()
    
    return render_template('users/add.html')

@users_bp.route('/edit/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """Edit existing user"""
    conn = get_db_connection()
    
    try:
        # Get user data
        user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('users.users_list'))
        
        # Get student data if user is a student
        student = None
        if user['role'] == 'student':
            student = conn.execute('SELECT * FROM students WHERE user_id = ?', (user_id,)).fetchone()
        
        if request.method == 'POST':
            # Debug: Print form data
            print(f"DEBUG: Form data received: {dict(request.form)}")
            
            # Get form data with proper validation
            full_name = request.form.get('full_name', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            address = request.form.get('address', '').strip()

            # Student-specific fields
            student_id = request.form.get('student_id', '').strip()
            class_name = request.form.get('class_name', '').strip()
            section = request.form.get('section', '').strip()
            parent_name = request.form.get('parent_name', '').strip()
            parent_phone = request.form.get('parent_phone', '').strip()
            status = request.form.get('status', 'active')
            
            # Validation
            errors = []
            
            if not full_name:
                errors.append('Full name is required.')
            
            if not email or '@' not in email:
                errors.append('Valid email is required.')
            
            # Check for existing email (excluding current user)
            existing_user = conn.execute(
                'SELECT id FROM users WHERE email = ? AND id != ?', (email, user_id)
            ).fetchone()
            
            if existing_user:
                errors.append('Email already exists.')
            
            # Check for existing student ID (excluding current student)
            if user['role'] == 'student' and student_id:
                existing_student = conn.execute(
                    'SELECT id FROM students WHERE student_id = ? AND user_id != ?', 
                    (student_id, user_id)
                ).fetchone()
                if existing_student:
                    errors.append('Student ID already exists.')
            
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('users/edit.html', user=user, student=student)
            
            # Update user
            try:
                cursor = conn.cursor()

                # Update users table
                print(f"DEBUG: Updating user {user_id} with data: {full_name}, {email}, {phone}, {address}")
                cursor.execute('''
                    UPDATE users
                    SET full_name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (full_name, email, phone, address, user_id))
                
                print(f"DEBUG: User table rows affected: {cursor.rowcount}")

                # Update student record if applicable
                if user['role'] == 'student' and student:
                    print(f"DEBUG: Updating student record for user {user_id}")
                    cursor.execute('''
                        UPDATE students
                        SET student_id = ?, class_name = ?, section = ?,
                            parent_name = ?, parent_phone = ?, status = ?
                        WHERE user_id = ?
                    ''', (student_id, class_name, section, parent_name, parent_phone, status, user_id))
                    print(f"DEBUG: Student table rows affected: {cursor.rowcount}")

                conn.commit()
                print(f"DEBUG: Successfully updated user {user_id}")
                flash(f'User "{full_name}" updated successfully!', 'success')
                return redirect(url_for('users.users_list'))
                
            except Exception as e:
                conn.rollback()
                print(f"DEBUG: Database error: {str(e)}")
                flash(f'Failed to update user: {str(e)}', 'error')
                return render_template('users/edit.html', user=user, student=student)
        
        # GET request - display form
        return render_template('users/edit.html', user=user, student=student)
        
    except Exception as e:
        print(f"DEBUG: General error in edit_user: {str(e)}")
        flash(f'An error occurred: {str(e)}', 'error')
        return redirect(url_for('users.users_list'))
    finally:
        conn.close()

@users_bp.route('/delete/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """Delete user"""
    conn = get_db_connection()
    
    # Get user data
    user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    if not user:
        flash('User not found.', 'error')
        return redirect(url_for('users.users_list'))
    
    # Prevent deleting the current admin user
    if user_id == int(request.form.get('current_user_id', 0)):
        flash('You cannot delete your own account.', 'error')
        return redirect(url_for('users.users_list'))
    
    try:
        # Delete user (cascade will handle related records)
        conn.execute('DELETE FROM users WHERE id = ?', (user_id,))
        conn.commit()
        flash(f'User "{user["full_name"]}" deleted successfully!', 'success')
    except Exception as e:
        conn.rollback()
        flash(f'Failed to delete user: {str(e)}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('users.users_list'))

@users_bp.route('/students')
@login_required
def students_list():
    """Display all students (accessible by teachers and admins)"""
    search = request.args.get('search', '')
    class_filter = request.args.get('class', '')
    status_filter = request.args.get('status', 'active')
    
    conn = get_db_connection()
    
    # Build query with filters
    query = '''
        SELECT s.*, u.full_name, u.email, u.phone, u.address, u.created_at
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE 1=1
    '''
    params = []
    
    if search:
        query += ' AND (u.full_name LIKE ? OR s.student_id LIKE ? OR u.email LIKE ?)'
        search_param = f'%{search}%'
        params.extend([search_param, search_param, search_param])
    
    if class_filter:
        query += ' AND s.class_name = ?'
        params.append(class_filter)
    
    if status_filter:
        query += ' AND s.status = ?'
        params.append(status_filter)
    
    query += ' ORDER BY s.student_id'
    
    students = conn.execute(query, params).fetchall()
    
    # Get unique classes for filter
    classes = conn.execute('SELECT DISTINCT class_name FROM students ORDER BY class_name').fetchall()
    
    conn.close()
    
    return render_template('users/students.html', 
                         students=students,
                         classes=classes,
                         search=search,
                         class_filter=class_filter,
                         status_filter=status_filter)

@users_bp.route('/teacher-assignments')
@login_required
@admin_required
def teacher_assignments():
    """Manage teacher class/subject assignments"""
    conn = get_db_connection()

    # Get all teachers
    teachers = conn.execute('''
        SELECT id, full_name, email FROM users
        WHERE role = 'teacher'
        ORDER BY full_name
    ''').fetchall()

    # Get all assignments with teacher info
    assignments = conn.execute('''
        SELECT ta.*, u.full_name as teacher_name
        FROM teacher_assignments ta
        JOIN users u ON ta.teacher_id = u.id
        ORDER BY u.full_name, ta.class_name, ta.subject
    ''').fetchall()

    # Get unique classes and subjects for dropdowns
    classes = conn.execute('SELECT DISTINCT class_name FROM students ORDER BY class_name').fetchall()
    subjects = conn.execute('SELECT DISTINCT subject FROM results ORDER BY subject').fetchall()

    conn.close()

    return render_template('users/teacher_assignments.html',
                         teachers=teachers,
                         assignments=assignments,
                         classes=classes,
                         subjects=subjects)

@users_bp.route('/add-teacher-assignment', methods=['POST'])
@login_required
@admin_required
def add_teacher_assignment():
    """Add a new teacher assignment"""
    teacher_id = request.form.get('teacher_id')
    class_name = request.form.get('class_name')
    subject = request.form.get('subject')
    section = request.form.get('section', '').strip() or None

    if not teacher_id or not class_name or not subject:
        flash('Teacher, class, and subject are required.', 'error')
        return redirect(url_for('users.teacher_assignments'))

    conn = get_db_connection()
    try:
        conn.execute('''
            INSERT INTO teacher_assignments (teacher_id, class_name, subject, section)
            VALUES (?, ?, ?, ?)
        ''', (teacher_id, class_name, subject, section))
        conn.commit()
        flash('Teacher assignment added successfully!', 'success')
    except Exception as e:
        conn.rollback()
        if 'UNIQUE constraint failed' in str(e):
            flash('This assignment already exists.', 'error')
        else:
            flash(f'Failed to add assignment: {str(e)}', 'error')
    finally:
        conn.close()

    return redirect(url_for('users.teacher_assignments'))

@users_bp.route('/delete-teacher-assignment/<int:assignment_id>', methods=['POST'])
@login_required
@admin_required
def delete_teacher_assignment(assignment_id):
    """Delete a teacher assignment"""
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM teacher_assignments WHERE id = ?', (assignment_id,))
        conn.commit()
        flash('Teacher assignment deleted successfully!', 'success')
    except Exception as e:
        conn.rollback()
        flash(f'Failed to delete assignment: {str(e)}', 'error')
    finally:
        conn.close()

    return redirect(url_for('users.teacher_assignments'))
