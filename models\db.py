#!/usr/bin/env python3
"""
Database Models and Connection Management
Handles SQLite database initialization and connection management
"""

import sqlite3
import os
from flask import current_app, g

def get_db_connection():
    """Get database connection - always creates a fresh connection"""
    try:
        # Try to get database path from Flask config
        db_path = current_app.config['DATABASE']
    except (RuntimeError, AttributeError):
        # If we're outside of application context, use default path
        db_path = 'database/database.db'

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def close_db(error):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

def init_db():
    """Initialize database with all required tables"""
    # Ensure database directory exists
    db_path = current_app.config['DATABASE']
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Users table for authentication and role management
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'student')),
                full_name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Students table for detailed student information
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                student_id TEXT UNIQUE NOT NULL,
                class_name TEXT NOT NULL,
                section TEXT,
                admission_date DATE,
                parent_name TEXT,
                parent_phone TEXT,
                emergency_contact TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'graduated')),
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        ''')
        
        # Results table for academic performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                subject TEXT NOT NULL,
                exam_type TEXT NOT NULL,
                marks_obtained REAL NOT NULL,
                total_marks REAL NOT NULL,
                grade TEXT,
                exam_date DATE,
                teacher_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
                FOREIGN KEY (teacher_id) REFERENCES users (id)
            )
        ''')
        
        # Financial records table for fee management
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS financial_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                fee_type TEXT NOT NULL,
                amount REAL NOT NULL,
                due_date DATE NOT NULL,
                paid_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'partial')),
                payment_method TEXT,
                remarks TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE
            )
        ''')

        # Teacher assignments table for class/subject assignments
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS teacher_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                teacher_id INTEGER NOT NULL,
                class_name TEXT NOT NULL,
                subject TEXT NOT NULL,
                section TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (teacher_id) REFERENCES users (id) ON DELETE CASCADE,
                UNIQUE(teacher_id, class_name, subject, section)
            )
        ''')

        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_results_student_id ON results(student_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_financial_student_id ON financial_records(student_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_teacher_assignments ON teacher_assignments(teacher_id, class_name, subject)')
        
        conn.commit()
        # Add sample teacher assignments
        sample_assignments = [
            (2, 'Grade 10', 'Mathematics', 'A'),  # teacher1 -> Grade 10 Math Section A
            (2, 'Grade 10', 'Physics', 'A'),     # teacher1 -> Grade 10 Physics Section A
            (2, 'Grade 11', 'Mathematics', None), # teacher1 -> Grade 11 Math All Sections
        ]

        for teacher_id, class_name, subject, section in sample_assignments:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO teacher_assignments (teacher_id, class_name, subject, section)
                    VALUES (?, ?, ?, ?)
                ''', (teacher_id, class_name, subject, section))
            except:
                pass  # Ignore if already exists

        print("✅ Database initialized successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def get_user_by_username(username):
    """Get user by username"""
    conn = get_db_connection()
    try:
        user = conn.execute(
            'SELECT * FROM users WHERE username = ?', (username,)
        ).fetchone()
        return user
    finally:
        conn.close()

def get_user_by_id(user_id):
    """Get user by ID"""
    conn = get_db_connection()
    try:
        user = conn.execute(
            'SELECT * FROM users WHERE id = ?', (user_id,)
        ).fetchone()
        return user
    finally:
        conn.close()

def get_all_users(role=None):
    """Get all users, optionally filtered by role"""
    conn = get_db_connection()
    if role:
        users = conn.execute(
            'SELECT * FROM users WHERE role = ? ORDER BY created_at DESC', (role,)
        ).fetchall()
    else:
        users = conn.execute(
            'SELECT * FROM users ORDER BY created_at DESC'
        ).fetchall()
    return users

def get_all_students():
    """Get all students with user information"""
    conn = get_db_connection()
    students = conn.execute('''
        SELECT s.*, u.full_name, u.email, u.phone, u.address
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.status = 'active'
        ORDER BY s.student_id
    ''').fetchall()
    return students

def get_student_results(student_id):
    """Get all results for a student"""
    conn = get_db_connection()
    results = conn.execute('''
        SELECT r.*, u.full_name as teacher_name
        FROM results r
        LEFT JOIN users u ON r.teacher_id = u.id
        WHERE r.student_id = ?
        ORDER BY r.exam_date DESC, r.subject
    ''', (student_id,)).fetchall()
    return results

def get_student_fees(student_id):
    """Get all financial records for a student"""
    conn = get_db_connection()
    fees = conn.execute('''
        SELECT * FROM financial_records
        WHERE student_id = ?
        ORDER BY due_date DESC
    ''', (student_id,)).fetchall()
    return fees

def calculate_grade(percentage):
    """Calculate grade based on percentage"""
    if percentage >= 90:
        return 'A+'
    elif percentage >= 80:
        return 'A'
    elif percentage >= 70:
        return 'B+'
    elif percentage >= 60:
        return 'B'
    elif percentage >= 50:
        return 'C+'
    elif percentage >= 40:
        return 'C'
    elif percentage >= 33:
        return 'D'
    else:
        return 'F'

def get_dashboard_stats():
    """Get statistics for dashboard"""
    conn = get_db_connection()
    
    stats = {}
    
    # Total users by role
    stats['total_users'] = conn.execute('SELECT COUNT(*) FROM users').fetchone()[0]
    stats['total_students'] = conn.execute('SELECT COUNT(*) FROM users WHERE role = "student"').fetchone()[0]
    stats['total_teachers'] = conn.execute('SELECT COUNT(*) FROM users WHERE role = "teacher"').fetchone()[0]
    stats['total_admins'] = conn.execute('SELECT COUNT(*) FROM users WHERE role = "admin"').fetchone()[0]
    
    # Active students
    stats['active_students'] = conn.execute('SELECT COUNT(*) FROM students WHERE status = "active"').fetchone()[0]
    
    # Financial stats
    stats['total_revenue'] = conn.execute('SELECT COALESCE(SUM(amount), 0) FROM financial_records WHERE status = "paid"').fetchone()[0]
    stats['pending_fees'] = conn.execute('SELECT COALESCE(SUM(amount), 0) FROM financial_records WHERE status = "pending"').fetchone()[0]
    stats['overdue_fees'] = conn.execute('SELECT COALESCE(SUM(amount), 0) FROM financial_records WHERE status = "overdue"').fetchone()[0]
    
    # Recent activities
    stats['recent_results'] = conn.execute('''
        SELECT r.subject, r.marks_obtained, r.total_marks, u.full_name as student_name, r.exam_date
        FROM results r
        JOIN students s ON r.student_id = s.id
        JOIN users u ON s.user_id = u.id
        ORDER BY r.created_at DESC
        LIMIT 5
    ''').fetchall()
    
    stats['recent_payments'] = conn.execute('''
        SELECT f.fee_type, f.amount, u.full_name as student_name, f.paid_date
        FROM financial_records f
        JOIN students s ON f.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE f.status = "paid" AND f.paid_date IS NOT NULL
        ORDER BY f.paid_date DESC
        LIMIT 5
    ''').fetchall()
    
    return stats

def get_student_by_user_id(user_id):
    """Get student record by user ID"""
    conn = get_db_connection()
    student = conn.execute('''
        SELECT s.*, u.full_name, u.email, u.phone, u.address
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE u.id = ?
    ''', (user_id,)).fetchone()
    conn.close()
    return student

def get_teacher_assignments(teacher_id):
    """Get all class/subject assignments for a teacher"""
    conn = get_db_connection()
    assignments = conn.execute('''
        SELECT * FROM teacher_assignments
        WHERE teacher_id = ?
        ORDER BY class_name, subject
    ''', (teacher_id,)).fetchall()
    conn.close()
    return assignments

def get_teacher_students(teacher_id):
    """Get all students that a teacher is assigned to teach"""
    conn = get_db_connection()
    students = conn.execute('''
        SELECT DISTINCT s.*, u.full_name, u.email
        FROM students s
        JOIN users u ON s.user_id = u.id
        JOIN teacher_assignments ta ON (
            ta.teacher_id = ? AND
            ta.class_name = s.class_name AND
            (ta.section IS NULL OR ta.section = s.section OR s.section IS NULL)
        )
        WHERE u.role = 'student'
        ORDER BY s.class_name, s.section, u.full_name
    ''', (teacher_id,)).fetchall()
    conn.close()
    return students

def is_teacher_assigned_to_student(teacher_id, student_id):
    """Check if a teacher is assigned to teach a specific student"""
    conn = get_db_connection()
    result = conn.execute('''
        SELECT COUNT(*) as count
        FROM students s
        JOIN teacher_assignments ta ON (
            ta.teacher_id = ? AND
            ta.class_name = s.class_name AND
            (ta.section IS NULL OR ta.section = s.section OR s.section IS NULL)
        )
        WHERE s.id = ?
    ''', (teacher_id, student_id)).fetchone()
    conn.close()
    return result['count'] > 0
