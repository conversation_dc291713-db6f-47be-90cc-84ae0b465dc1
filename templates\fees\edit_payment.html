<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Payment Record - EduManage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center text-white">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2"></i>
                                <span>+****************</span>
                            </div>
                            <span class="mx-3 opacity-50 d-none d-sm-inline">|</span>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-md-end justify-content-center text-white">
                            <span class="badge bg-warning me-2 px-3 py-2">Edit Payment</span>
                            <span class="d-none d-sm-inline">{{ fee.student_name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark main-nav">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/dashboard/">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    EduManage
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/fees/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Fees
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1 py-4">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="bi bi-credit-card me-3"></i>
                            Edit Payment Record
                        </h1>
                        <p class="page-subtitle">Update payment information for {{ fee.student_name }}</p>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Current Fee Info -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-check-circle me-2"></i>
                                Current Payment Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Student:</strong> {{ fee.student_name }}</p>
                                    <p><strong>Student ID:</strong> {{ fee.student_id }}</p>
                                    <p><strong>Fee Type:</strong> {{ fee.fee_type }}</p>
                                    <p><strong>Amount:</strong> ${{ "%.2f"|format(fee.amount) }}</p>
                                    <p><strong>Status:</strong> 
                                        <span class="badge bg-success">Paid</span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Current Payment Date:</strong> {{ fee.paid_date.strftime('%Y-%m-%d') if fee.paid_date else 'N/A' }}</p>
                                    <p><strong>Current Payment Method:</strong> {{ fee.payment_method.title() if fee.payment_method else 'N/A' }}</p>
                                    <p><strong>Due Date:</strong> {{ fee.due_date.strftime('%Y-%m-%d') if fee.due_date else 'N/A' }}</p>
                                    <p><strong>Created:</strong> {{ fee.created_at.strftime('%Y-%m-%d') if fee.created_at else 'N/A' }}</p>
                                </div>
                            </div>
                            {% if fee.remarks %}
                            <div class="mt-3">
                                <h6 class="text-muted">Current Remarks:</h6>
                                <p class="text-muted">{{ fee.remarks }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Edit Payment Form -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-pencil-square me-2"></i>
                                Update Payment Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/fees/edit-payment/{{ fee.id }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_date" class="form-label">
                                                <i class="bi bi-calendar me-1"></i>Payment Date *
                                            </label>
                                            <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                                   value="{{ fee.paid_date.strftime('%Y-%m-%d') if fee.paid_date else '' }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">
                                                <i class="bi bi-credit-card me-1"></i>Payment Method *
                                            </label>
                                            <select class="form-select" id="payment_method" name="payment_method" required>
                                                <option value="">Select Payment Method</option>
                                                <option value="cash" {{ 'selected' if fee.payment_method == 'cash' else '' }}>Cash</option>
                                                <option value="bank_transfer" {{ 'selected' if fee.payment_method == 'bank_transfer' else '' }}>Bank Transfer</option>
                                                <option value="check" {{ 'selected' if fee.payment_method == 'check' else '' }}>Check</option>
                                                <option value="credit_card" {{ 'selected' if fee.payment_method == 'credit_card' else '' }}>Credit Card</option>
                                                <option value="debit_card" {{ 'selected' if fee.payment_method == 'debit_card' else '' }}>Debit Card</option>
                                                <option value="online" {{ 'selected' if fee.payment_method == 'online' else '' }}>Online Payment</option>
                                                <option value="mobile_payment" {{ 'selected' if fee.payment_method == 'mobile_payment' else '' }}>Mobile Payment</option>
                                                <option value="other" {{ 'selected' if fee.payment_method == 'other' else '' }}>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="remarks" class="form-label">
                                                <i class="bi bi-chat-text me-1"></i>Payment Remarks
                                            </label>
                                            <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                                      placeholder="Optional payment notes or remarks">{{ fee.remarks or '' }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Summary -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="bi bi-info-circle me-2"></i>Payment Summary
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Student:</strong> {{ fee.student_name }}</p>
                                            <p class="mb-1"><strong>Fee Type:</strong> {{ fee.fee_type }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Amount:</strong> ${{ "%.2f"|format(fee.amount) }}</p>
                                            <p class="mb-0"><strong>Status:</strong> <span class="badge bg-success">Paid</span></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="submit" class="btn btn-primary btn-lg w-100">
                                            <i class="bi bi-check-circle me-2"></i>
                                            Update Payment Record
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="/fees/" class="btn btn-secondary btn-lg w-100">
                                            <i class="bi bi-arrow-left me-2"></i>
                                            Cancel
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-main mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 EduManage. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Professional Student Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('🎯 THEMED: Payment edit form loaded');
        
        // Set maximum date to today for payment date
        document.getElementById('payment_date').max = new Date().toISOString().split('T')[0];
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const paymentDate = new Date(document.getElementById('payment_date').value);
            const today = new Date();
            
            if (paymentDate > today) {
                e.preventDefault();
                alert('Payment date cannot be in the future');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Updating...';
                submitBtn.disabled = true;
            }
        });
    </script>
</body>
</html>
