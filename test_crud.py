#!/usr/bin/env python3
"""
Test CRUD Operations
Quick test to verify database operations are working
"""

import sqlite3
import sys
import os

def test_database_operations():
    """Test basic database CRUD operations"""
    print("🧪 Testing Database CRUD Operations...")
    
    # Check if database exists
    db_path = 'database/database.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("✅ Database connection successful")
        
        # Test 1: Read operations
        print("\n📖 Testing READ operations...")
        
        # Get all users
        users = cursor.execute('SELECT * FROM users').fetchall()
        print(f"   Users found: {len(users)}")
        
        # Get admin users
        admins = cursor.execute('SELECT * FROM users WHERE role = "admin"').fetchall()
        print(f"   Admin users: {len(admins)}")
        
        if admins:
            admin = admins[0]
            print(f"   First admin: {admin['username']} ({admin['full_name']})")
        
        # Get students
        students = cursor.execute('''
            SELECT s.*, u.full_name, u.email 
            FROM students s 
            JOIN users u ON s.user_id = u.id
        ''').fetchall()
        print(f"   Students found: {len(students)}")
        
        # Get results
        results = cursor.execute('SELECT * FROM results').fetchall()
        print(f"   Results found: {len(results)}")
        
        # Get fees
        fees = cursor.execute('SELECT * FROM financial_records').fetchall()
        print(f"   Fee records found: {len(fees)}")
        
        # Test 2: Update operations
        print("\n✏️ Testing UPDATE operations...")
        
        if users:
            test_user = users[0]
            original_phone = test_user['phone']
            test_phone = "555-TEST-123"
            
            # Update user phone
            cursor.execute('''
                UPDATE users 
                SET phone = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (test_phone, test_user['id']))
            
            # Verify update
            updated_user = cursor.execute(
                'SELECT phone FROM users WHERE id = ?', 
                (test_user['id'],)
            ).fetchone()
            
            if updated_user['phone'] == test_phone:
                print(f"   ✅ User update successful: {test_phone}")
                
                # Restore original value
                cursor.execute('''
                    UPDATE users 
                    SET phone = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (original_phone, test_user['id']))
                print(f"   ✅ Original value restored: {original_phone}")
            else:
                print(f"   ❌ User update failed")
        
        # Test 3: Insert and Delete operations
        print("\n➕ Testing INSERT/DELETE operations...")
        
        # Insert test user
        test_username = "test_crud_user"
        cursor.execute('''
            INSERT INTO users (username, password_hash, role, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        ''', (test_username, "test_hash", "student", "Test CRUD User", "<EMAIL>"))
        
        test_user_id = cursor.lastrowid
        print(f"   ✅ Test user created with ID: {test_user_id}")
        
        # Verify insert
        test_user = cursor.execute(
            'SELECT * FROM users WHERE id = ?', 
            (test_user_id,)
        ).fetchone()
        
        if test_user and test_user['username'] == test_username:
            print(f"   ✅ Test user verified: {test_user['full_name']}")
            
            # Delete test user
            cursor.execute('DELETE FROM users WHERE id = ?', (test_user_id,))
            
            # Verify deletion
            deleted_user = cursor.execute(
                'SELECT * FROM users WHERE id = ?', 
                (test_user_id,)
            ).fetchone()
            
            if not deleted_user:
                print(f"   ✅ Test user deleted successfully")
            else:
                print(f"   ❌ Test user deletion failed")
        else:
            print(f"   ❌ Test user verification failed")
        
        # Commit all changes
        conn.commit()
        print("\n💾 All changes committed successfully")
        
        conn.close()
        print("✅ Database connection closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_specific_user_update():
    """Test updating a specific user (like user ID 2)"""
    print("\n🎯 Testing specific user update (ID: 2)...")
    
    db_path = 'database/database.db'
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get user 2
        user = cursor.execute('SELECT * FROM users WHERE id = 2').fetchone()
        if not user:
            print("   ❌ User ID 2 not found")
            return False
        
        print(f"   📋 Current user: {user['full_name']} ({user['email']})")
        
        # Test update
        new_email = f"updated_{user['email']}"
        cursor.execute('''
            UPDATE users 
            SET email = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = 2
        ''', (new_email,))
        
        rows_affected = cursor.rowcount
        print(f"   📊 Rows affected: {rows_affected}")
        
        # Verify update
        updated_user = cursor.execute('SELECT * FROM users WHERE id = 2').fetchone()
        if updated_user['email'] == new_email:
            print(f"   ✅ Update successful: {new_email}")
            
            # Restore original
            cursor.execute('''
                UPDATE users 
                SET email = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = 2
            ''', (user['email'],))
            print(f"   ✅ Original email restored: {user['email']}")
        else:
            print(f"   ❌ Update failed")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Specific user update test failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    print("🚀 Starting CRUD Operations Test Suite")
    print("=" * 50)
    
    # Test general database operations
    general_test = test_database_operations()
    
    # Test specific user update
    specific_test = test_specific_user_update()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   General CRUD Operations: {'✅ PASSED' if general_test else '❌ FAILED'}")
    print(f"   Specific User Update: {'✅ PASSED' if specific_test else '❌ FAILED'}")
    
    if general_test and specific_test:
        print("\n🎉 All tests passed! CRUD operations are working correctly.")
        sys.exit(0)
    else:
        print("\n⚠️ Some tests failed. Check the database and application logic.")
        sys.exit(1)
